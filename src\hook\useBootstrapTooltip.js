import { useEffect } from 'react';
import { Tooltip } from 'bootstrap';

/**
 * @param {Array} dependencies 
 */
const useBootstrapTooltip = (dependencies = []) => {
    useEffect(() => {

        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');

        const tooltipInstances = [...tooltipTriggerList].map(tooltipTriggerEl => new Tooltip(tooltipTriggerEl));

        return () => {
            tooltipInstances.forEach(tooltip => tooltip.dispose());
        };
    }, dependencies);
};

export default useBootstrapTooltip;