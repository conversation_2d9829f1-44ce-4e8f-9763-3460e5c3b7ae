// src/client/addbankform/BidvPersonalOpenApiForm.jsx

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useBankApi from "../../callapi/Bank.jsx";

const BidvPersonalOpenApiForm = ({ bankName }) => {
  // --- State cho dữ liệu form ---
  const [accName, setAccName] = useState("");
  const [accEmail, setAccEmail] = useState("");
  const [accMobile, setAccMobile] = useState("");
  const [cccd, setCccd] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [merchantId, setMerchantId] = useState("");
  const [agree, setAgree] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otp, setOtp] = useState("");
  const [lastProcessedResponse, setLastProcessedResponse] = useState(null);

  // --- State quản lý thông báo (gộp chung) ---
  const [notification, setNotification] = useState({
    error: null,
    success: null,
  });

  // --- Hooks cho API và điều hướng ---
  const {
    data: apiResponse,
    loading: isLoading,
    error: apiError,
    callApi,
  } = useBankApi();
  const navigate = useNavigate();

  // console.log({ bankName });
  /**
   * Hàm xử lý chính khi người dùng nhấn nút submit.
   */
  const handleSubmit = async (event) => {
    event.preventDefault();
    setNotification({ error: null, success: null });
    const userId = localStorage.getItem("user_id");

    if (!userId) {
      setNotification({
        error: "Lỗi xác thực: Không tìm thấy User ID. Vui lòng đăng nhập lại.",
        success: null,
      });
      return;
    }

    let apiBody;
    if (showOtpInput) {
      // Giai đoạn 2: Gửi thông tin kèm mã OTP để xác nhận
      apiBody = {
        accountNumber,
        accEmail,
        merchantId,
        shortName: bankName,
        type: "openapi",
        action: "confirm_otp",
        user_id: userId,
        otp,
      };
    } else {
      // Giai đoạn 1: Gửi thông tin tài khoản ban đầu
      if (!agree) {
        setNotification({
          error: "Bạn phải đồng ý với điều khoản và điều kiện.",
          success: null,
        });
        return;
      }
      apiBody = {
        accName,
        accountNumber,
        accEmail,
        merchantId,
        shortName: bankName,
        action: "add",
        user_id: userId,
        cccd,
        accMobile,
        type: "openapi",
      };
    }

    await callApi(apiBody);
  };

  useEffect(() => {
    // Tránh xử lý lại cùng một response
    if (apiResponse && apiResponse === lastProcessedResponse) {
      return;
    }

    if (apiError) {
      setNotification({ error: apiError, success: null });
      return;
    }

    if (apiResponse && apiResponse.status === true) {
      // Đánh dấu response đã được xử lý
      setLastProcessedResponse(apiResponse);

      // Trường hợp 1: API yêu cầu OTP và chưa hiển thị form OTP
      if (apiResponse.OTP === 1 && !showOtpInput) {
        setShowOtpInput(true);
        setNotification({
          success: "Gửi OTP Thành công. Vui lòng nhập mã OTP để hoàn tất.",
          error: null,
        });
        return;
      }

      // Trường hợp 2: Không cần OTP hoặc đã xác nhận OTP thành công
      if (apiResponse.OTP !== 1 || (apiResponse.OTP === 1 && showOtpInput)) {
        const finalMessage = apiResponse.message || "Thao tác thành công!";
        setNotification({
          success: `${finalMessage} Đang chuyển hướng...`,
          error: null,
        });

        setTimeout(() => {
          navigate(`/client/account-bank/${bankName}`, {
            state: { justAddedBank: true },
          });
        }, 2000);
      }
    } else if (apiResponse && apiResponse.status === false) {
      setNotification({
        error: apiResponse.message || "Có lỗi xảy ra khi xử lý yêu cầu",
        success: null,
      });
      setLastProcessedResponse(apiResponse);
    }
  }, [
    apiResponse,
    apiError,
    navigate,
    bankName,
    showOtpInput,
    lastProcessedResponse,
  ]);

  return (
    <form onSubmit={handleSubmit}>
      {/* Khu vực hiển thị thông báo đã được gộp lại */}
      {notification.error && (
        <div className="alert alert-danger">{notification.error}</div>
      )}
      {notification.success && (
        <div className="alert alert-success">{notification.success}</div>
      )}

      {/* Các trường input */}
      <div className="mt-20">
        <label className="form-label">Họ và tên *</label>
        <input
          className="form-control"
          type="text"
          value={accName}
          onChange={(e) => setAccName(e.target.value.toUpperCase())}
          style={{ textTransform: "uppercase" }}
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Email *</label>
        <input
          className="form-control"
          type="email"
          value={accEmail}
          onChange={(e) => setAccEmail(e.target.value)}
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số điện thoại *</label>
        <input
          className="form-control"
          type="number"
          value={accMobile}
          onChange={(e) => setAccMobile(e.target.value)}
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số căn cước công dân *</label>
        <input
          className="form-control"
          type="number"
          value={cccd}
          onChange={(e) => setCccd(e.target.value)}
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số tài khoản {bankName} *</label>
        <input
          className="form-control"
          type="number"
          value={accountNumber}
          onChange={(e) => setAccountNumber(e.target.value)}
          required
          disabled={isLoading}
        />
      </div>
      <div className="mt-20">
        <label className="form-label">Tạo số tài khoản ảo *</label>
        <div className="input-group">
          <span className="input-group-text" id="basic-addon1">
            963869
          </span>
          <input
            className="form-control"
            type="text"
            value={merchantId}
            onChange={(e) => setMerchantId(e.target.value.toUpperCase())}
            required
            disabled={isLoading}
            placeholder="Nhập số hoặc chữ (ví dụ: 963869VYNT)"
          />
        </div>
      </div>

      {/* Trường nhập OTP */}
      {showOtpInput && (
        <div className="mt-20">
          <label className="form-label fw-bold text-danger">Mã OTP *</label>
          <input
            className="form-control"
            type="text"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            required
            placeholder="Nhập mã OTP đã được gửi đến bạn"
            autoFocus
          />
        </div>
      )}

      {/* Checkbox điều khoản */}
      {!showOtpInput && (
        <div className="form-check mt-20">
          <input
            className="form-check-input"
            type="checkbox"
            id="agreePay2s"
            checked={agree}
            onChange={(e) => setAgree(e.target.checked)}
            required
          />
          <label className="form-check-label small" htmlFor="agreePay2s">
            Bằng cách cung cấp thông tin cho Pay2S. Bạn đã đồng ý với{" "}
            <a
              href="https://pay2s.vn/chinh-sach-bao-mat"
              className="text-primary-600 fw-semibold"
              target="_blank"
              rel="noopener noreferrer"
            >
              Chính sách bảo mật *
            </a>{" "}
            của Pay2S và cho phép Pay2S truy xuất thông tin tài chính từ ngân
            hàng của bạn và Đồng ý nhận thông báo tiền về từ ngân hàng đến hệ
            thống Pay2S.
          </label>
        </div>
      )}

      {/* Nút Submit */}
      <div className="mt-20">
        <button type="submit" className="btn btn-success" disabled={isLoading}>
          {isLoading ? "ĐANG XỬ LÝ..." : "THÊM TÀI KHOẢN"}
        </button>
      </div>
    </form>
  );
};

export default BidvPersonalOpenApiForm;
