// src/client/addbankform/AcbBusinessOpenApiForm.jsx

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useBankApi from "../../callapi/Bank.jsx";

const AcbBusinessOpenApiForm = ({ bankName }) => {
  // --- State cho dữ liệu form ---
  const [accName, setAccName] = useState("");
  const [accMobile, setAccMobile] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [username, setUsername] = useState("");
  const [agree, setAgree] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otp, setOtp] = useState("");
  // --- State quản lý thông báo (gộp chung) ---
  const [notification, setNotification] = useState({
    error: null,
    success: null,
  });

  // --- Hooks cho API và điều hướng ---
  const {
    data: apiResponse,
    loading: isLoading,
    error: apiError,
    callApi,
  } = useBankApi();
  const navigate = useNavigate();
  /**
   * Hàm xử lý chính khi người dùng nhấn nút submit.
   */
  const handleSubmit = async (event) => {
    event.preventDefault();
    setNotification({ error: null, success: null });
    const userId = localStorage.getItem("user_id");

    if (!userId) {
      setNotification({
        error: "Lỗi xác thực: Không tìm thấy User ID. Vui lòng đăng nhập lại.",
        success: null,
      });
      return;
    }

    let apiBody;
    if (showOtpInput) {
      // Giai đoạn 2: Gửi thông tin kèm mã OTP để xác nhận
      apiBody = {
        username,
        accountNumber,
        shortName: bankName,
        type: "openapi",
        action: "confirm_otp",
        user_id: userId,
        otp,
      };
    } else {
      // Giai đoạn 1: Gửi thông tin tài khoản ban đầu
      if (!agree) {
        setNotification({
          error: "Bạn phải đồng ý với điều khoản và điều kiện.",
          success: null,
        });
        return;
      }
      apiBody = {
        accName,
        accMobile,
        accountNumber,
        username,
        shortName: bankName,
        action: "add",
        user_id: userId,
        type: "openapi",
      };
    }
    await callApi(apiBody);
  };

  useEffect(() => {
    if (apiError) {
      setNotification({ error: apiError, success: null });
    }

    if (apiResponse && apiResponse.status === true) {
      if (apiResponse.OTP === 1 && !showOtpInput) {
        // Cần OTP, chuyển sang giao diện nhập OTP
        setShowOtpInput(true);
        setNotification({
          success: "Gửi OTP Thành công. Vui lòng nhập mã OTP để hoàn tất.",
          error: null,
        });
      } else {
        // Hoàn tất thành công
        const finalMessage = apiResponse.message || "Thao tác thành công!";
        setNotification({
          success: `${finalMessage} Đang chuyển hướng...`,
          error: null,
        });

        const timer = setTimeout(() => {
          navigate(`/client/account-bank/${bankName}`, {
            state: { justAddedBank: true },
          });
        }, 2000);

        return () => clearTimeout(timer);
      }
    }
  }, [apiResponse, apiError, navigate, bankName]);

  return (
    <form onSubmit={handleSubmit}>
      {/* Khu vực hiển thị thông báo đã được gộp lại */}
      {notification.error && (
        <div className="alert alert-danger">{notification.error}</div>
      )}
      {notification.success && (
        <div className="alert alert-success">{notification.success}</div>
      )}

      {/* Các trường input */}
      <div className="mt-20">
        <label className="form-label">Tên công ty *</label>
        <input
          className="form-control"
          type="text"
          value={accName}
          onChange={(e) => setAccName(e.target.value.toUpperCase())}
          style={{ textTransform: "uppercase" }}
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số điện thoại *</label>
        <input
          className="form-control"
          type="number"
          value={accMobile}
          onChange={(e) => setAccMobile(e.target.value)}
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số tài khoản {bankName} *</label>
        <input
          className="form-control"
          type="number"
          value={accountNumber}
          onChange={(e) => setAccountNumber(e.target.value)}
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Username ACB Onebiz *</label>
        <input
          className="form-control"
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
          disabled={isLoading}
          placeholder="Nhập username ACB Onebiz"
        />
      </div>

      {/* Trường nhập OTP */}
      {showOtpInput && (
        <div className="mt-20">
          <label className="form-label fw-bold text-danger">Mã OTP *</label>
          <input
            className="form-control"
            type="text"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            required
            placeholder="Nhập mã OTP đã được gửi đến bạn"
            autoFocus
          />
        </div>
      )}

      {/* Checkbox điều khoản */}
      {!showOtpInput && (
        <div className="form-check mt-20">
          <input
            className="form-check-input"
            type="checkbox"
            id="agreePay2s"
            checked={agree}
            onChange={(e) => setAgree(e.target.checked)}
            required
          />
          <label className="form-check-label small" htmlFor="agreePay2s">
            Bằng cách cung cấp thông tin cho Pay2S. Bạn đã đồng ý với{" "}
            <a
              href="https://pay2s.vn/chinh-sach-bao-mat"
              className="text-primary-600 fw-semibold"
              target="_blank"
              rel="noopener noreferrer"
            >
              Chính sách bảo mật *
            </a>{" "}
            của Pay2S và cho phép Pay2S truy xuất thông tin tài chính từ ngân
            hàng của bạn và Đồng ý nhận thông báo tiền về từ ngân hàng đến hệ
            thống Pay2S.
          </label>
        </div>
      )}

      {/* Nút Submit */}
      <div className="mt-20">
        <button type="submit" className="btn btn-success" disabled={isLoading}>
          {isLoading ? "ĐANG XỬ LÝ..." : "THÊM TÀI KHOẢN"}
        </button>
      </div>
    </form>
  );
};

export default AcbBusinessOpenApiForm;
