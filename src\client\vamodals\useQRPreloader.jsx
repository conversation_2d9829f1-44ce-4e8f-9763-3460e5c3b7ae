import { useEffect, useRef } from "react";

// Cache cho QR images để tránh load lại
export const qrImageCache = new Map();

export const useQRPreloader = (accountInfo) => {
  const abortControllerRef = useRef(null);

  useEffect(() => {
    if (accountInfo) {
      preloadQRImage(accountInfo);
    }

    // Cleanup
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [accountInfo]);

  const preloadQRImage = async (accountInfo) => {
    if (!accountInfo) return;

    const baseUrl = "https://payment.pay2s.vn/quicklink";
    const bankName = accountInfo.shortBankName || "BIDV";
    const accountNumber =
      accountInfo.vaNumber || accountInfo.accountNumber || "";
    const accountName = encodeURIComponent(
      accountInfo.name || accountInfo.username || ""
    );

    const params = new URLSearchParams({
      amount: "",
      memo: "",
      is_mask: "0",
      bg: "0",
    });

    const url = `${baseUrl}/${bankName}/${accountNumber}/${accountName}?${params.toString()}`;

    // Tạo cache key
    const cacheKey = `${bankName}-${accountNumber}-${accountName}`;

    // Kiểm tra cache trước
    if (qrImageCache.has(cacheKey)) {
      return;
    }

    // Hủy request cũ nếu có
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    // Danh sách URLs từ Pay2S để thử song song
    const imageUrls = [
      `${url}&format=png`,
      `${url}&format=image`,
      `${baseUrl}/image/${bankName}/${accountNumber}/${accountName}?${params.toString()}`,
      `${url}&export=image`,
      `${url}&download=qr`,
      `${url}&type=qr`,
      `${url}.png`,
      `${url}.jpg`,
      `${url}&output=image`,
      `${url}&qr=image`,
    ];

    try {
      const workingUrl = await findWorkingImageUrl(
        imageUrls,
        abortControllerRef.current.signal
      );
      if (workingUrl && !abortControllerRef.current.signal.aborted) {
        qrImageCache.set(cacheKey, workingUrl); // Cache kết quả
        console.log("QR preloaded for:", cacheKey);
      }
    } catch (error) {
      if (!abortControllerRef.current.signal.aborted) {
        console.log("QR preload failed for:", cacheKey, error);
      }
    }
  };

  // Hàm tìm URL hoạt động bằng Promise.any
  const findWorkingImageUrl = async (urls, signal) => {
    const testPromises = urls.map(async (url) => {
      return new Promise((resolve, reject) => {
        if (signal.aborted) {
          reject(new Error("Aborted"));
          return;
        }

        const img = new Image();
        const timeout = setTimeout(() => {
          reject(new Error(`Timeout for ${url}`));
        }, 3000); // 3s timeout cho mỗi URL

        img.onload = () => {
          clearTimeout(timeout);
          resolve(url);
        };

        img.onerror = () => {
          clearTimeout(timeout);
          reject(new Error(`Failed to load ${url}`));
        };

        // Kiểm tra abort signal
        if (signal.aborted) {
          clearTimeout(timeout);
          reject(new Error("Aborted"));
          return;
        }

        img.src = url;
      });
    });

    // Trả về URL đầu tiên load thành công
    return Promise.any(testPromises);
  };
};
