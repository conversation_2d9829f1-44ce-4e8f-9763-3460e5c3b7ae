import React, { useState, useEffect, useCallback } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { bankLogos } from "./ImportImage.jsx";
import useBankApi from "../callapi/Bank.jsx";
import useBootstrapTooltip from "../hook/useBootstrapTooltip.js";

// Import form cá nhân Pay2s API
import Pay2sApiForm from "./addbankform/Pay2sApiForm";
// Import form Business Pay2s API
import Pay2sBusinessForm from "./addbankform/Pay2sBusinessForm";

// Import form cá nhân Open API
import BidvPersonalOpenApiForm from "./addbankform/BidvPersonalOpenApiForm";
import MbPersonalOpenApiForm from "./addbankform/MbPersonalOpenApiForm";
import AcbPersonalOpenApiForm from "./addbankform/AcbPersonalOpenApiForm";

// Import form Business Open API
import MbBusinessOpenApiForm from "./addbankform/MbBusinessOpenApiForm";
import AcbBusinessOpenApiForm from "./addbankform/AcbBusinessOpenApiForm";

const staticBankConfig = {
  BIDV: {
    logo: bankLogos.logoBidv,
    accType: ["Cá nhân"],
    support: ["OpenBanking", "Tài khoản ảo VA"],
  },
  ACB: {
    logo: bankLogos.logoAcb,
    accType: ["Cá nhân", "Doanh nghiệp"],
    support: ["OpenBanking", "Tài khoản ảo VA"],
  },
  MBB: {
    logo: bankLogos.logoMbb,
    accType: ["Cá nhân", "Doanh nghiệp"],
    support: ["OpenBanking", "Đồng bộ giao dịch tiền vào - tiền ra"],
  },
  VCB: {
    logo: bankLogos.logoVcb,
    accType: ["Cá nhân", "Doanh nghiệp"],
    support: ["Đồng bộ giao dịch tiền vào - tiền ra"],
  },
  VTB: {
    logo: bankLogos.logoVtb,
    accType: ["Cá nhân", "Doanh nghiệp"],
    support: ["Đồng bộ giao dịch tiền vào - tiền ra"],
  },
  SEAB: {
    logo: bankLogos.logoSeab,
    accType: ["Cá nhân"],
    support: ["Đồng bộ giao dịch tiền vào - tiền ra"],
  },
};

const formMapping = {
  BIDV: {
    personal: {
      openapi: BidvPersonalOpenApiForm,
      pay2s: Pay2sApiForm,
    },
  },
  MBB: {
    personal: {
      openapi: MbPersonalOpenApiForm,
      pay2s: Pay2sApiForm,
    },
    business: {
      openapi: MbBusinessOpenApiForm,
      pay2s: Pay2sBusinessForm,
    },
  },
  ACB: {
    personal: {
      openapi: AcbPersonalOpenApiForm,
      pay2s: Pay2sApiForm,
    },
    business: {
      openapi: AcbBusinessOpenApiForm,
      pay2s: Pay2sBusinessForm,
    },
  },
  VCB: {
    personal: {
      pay2s: Pay2sApiForm,
    },
    business: {
      pay2s: Pay2sBusinessForm,
    },
  },
  VTB: {
    // Tương tự VCB
    personal: {
      pay2s: Pay2sApiForm,
    },
    business: {
      pay2s: Pay2sBusinessForm,
    },
  },
  SEAB: {
    personal: {
      pay2s: Pay2sApiForm,
    },
  },
};

const LinkBankPage = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // States
  const [activeStep, setActiveStep] = useState(1);
  const [selectedBank, setSelectedBank] = useState(null);
  const [accountType, setAccountType] = useState("personal");
  const [apiType, setApiType] = useState("openapi");
  const [activeBankList, setActiveBankList] = useState([]);
  const [isAutoSelected, setIsAutoSelected] = useState(false);
  const { data: apiResponse, loading, error, callApi } = useBankApi();
  const apiTypeSelect = ["ACB", "VCB", "VTB", "MBB"];
  const accoutTypeSelect = ["ACB", "MBB", "BIDV"];

  // Lấy bank parameter từ URL
  const urlParams = new URLSearchParams(location.search);
  const bankFromUrl = urlParams.get("bank");

  // Logic gọi và xử lý API
  const fetchBankList = useCallback(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      callApi({ action: "list_bank", user_id: userId });
    } else {
      console.error("Lỗi: Không tìm thấy user_id trong localStorage.");
    }
  }, [callApi]);

  useEffect(() => {
    fetchBankList();
  }, [fetchBankList]);

  useEffect(() => {
    if (apiResponse && apiResponse.status === true) {
      const allBanksFromApi = apiResponse.message;

      const processedList = allBanksFromApi
        .filter((bank) => bank.status === "Active")
        .map((activeBank) => {
          const config = staticBankConfig[activeBank.shortBankName];
          if (!config) {
            console.warn(
              `Bỏ qua ngân hàng do không tìm thấy config cho key: "${activeBank.shortBankName}"`
            );
            return null;
          }

          const isPartnerBoolean =
            activeBank.isPartner === true ||
            (typeof activeBank.isPartner === "string" &&
              activeBank.isPartner.trim() === "true");

          return {
            ...config,
            ...activeBank,
            isPartner: isPartnerBoolean,
          };
        })
        .filter(Boolean);
      const sortedList = processedList.sort((bankA, bankB) => {
        const partnerSort = Number(bankB.isPartner) - Number(bankA.isPartner);

        if (partnerSort !== 0) {
          return partnerSort;
        }
        return bankA.accType.length - bankB.accType.length;
      });

      setActiveBankList(sortedList);
    }
  }, [apiResponse]);

  // Tách riêng useEffect để xử lý auto-selection
  useEffect(() => {
    if (
      activeBankList.length > 0 &&
      bankFromUrl &&
      !selectedBank &&
      !isAutoSelected
    ) {
      const targetBank = activeBankList.find(
        (bank) => bank.shortBankName === bankFromUrl.toUpperCase()
      );
      if (targetBank) {
        console.log(`Auto-selecting bank ${bankFromUrl} from URL`);
        setSelectedBank(targetBank);
        setActiveStep(2);

        const supportsOpenApi = accoutTypeSelect.includes(
          targetBank.shortBankName
        );
        setApiType(supportsOpenApi ? "openapi" : "pay2s");
        setAccountType("personal");
        setIsAutoSelected(true);
      }
    }
  }, [
    activeBankList,
    bankFromUrl,
    selectedBank,
    isAutoSelected,
    accoutTypeSelect,
  ]);

  const handleConnectClick = (bank) => {
    setSelectedBank(bank);
    setActiveStep(2);

    const supportsOpenApi = accoutTypeSelect.includes(bank.shortBankName);

    setApiType(supportsOpenApi ? "openapi" : "pay2s");
    setAccountType("personal");

    // Cập nhật URL parameter để phản ánh ngân hàng được chọn (chỉ khi user chọn thủ công)
    if (!isAutoSelected) {
      navigate(`/client/add-bank?bank=${bank.shortBankName}`, {
        replace: true,
      });
    }

    // Reset auto-select flag
    setIsAutoSelected(false);
  };

  const handleGoBack = () => {
    setActiveStep(1);
    setSelectedBank(null);
    setIsAutoSelected(false);

    // Xóa URL parameter khi quay lại
    navigate("/client/add-bank", { replace: true });
  };

  const renderDynamicForm = () => {
    if (!selectedBank) return null;

    const ComponentToRender =
      formMapping[selectedBank.shortBankName]?.[accountType]?.[apiType];

    if (ComponentToRender) {
      return <ComponentToRender bankName={selectedBank.shortBankName} />;
    }

    return <p>Form cho cấu hình này chưa được hỗ trợ.</p>;
  };

  useBootstrapTooltip([activeBankList, activeStep]);
  return (
    <MasterLayout>
      <Breadcrumb title="Liên kết tài khoản ngân hàng" />
      <div className="container-fluid">
        {/* Wizard Steps */}
        <div className="row g-3 mb-4">
          {/* ------ STEP 1 ------ */}
          <div className="col-md-6">
            <div
              className="p-3 border rounded h-100"
              style={{ cursor: "pointer" }}
              onClick={() => setActiveStep(1)}
            >
              <div className="d-flex align-items-center">
                {/* Vòng tròn số 1 */}
                <div
                  className="rounded-circle d-flex align-items-center justify-content-center me-3"
                  style={{
                    width: 32,
                    height: 32,
                    fontWeight: "bold",

                    border:
                      activeStep === 1
                        ? "2px solid #198754"
                        : "1px solid #dee2e6",
                    color: activeStep === 1 ? "#198754" : "inherit",
                  }}
                >
                  1
                </div>
                {/* Khối văn bản cho Step 1 */}
                <div>
                  <p
                    className={`mb-0 fw-bold ${
                      activeStep === 1 ? "text-success" : ""
                    }`}
                  >
                    Danh sách ngân hàng
                  </p>
                  <small className="text-muted">
                    Chọn ngân hàng liên kết phù hợp của bạn
                  </small>
                </div>
              </div>
            </div>
          </div>

          {/* ------ STEP 2 ------ */}
          <div className="col-md-6">
            <div
              className="p-3 border rounded h-100"
              style={{ cursor: "default" }}
            >
              <div className="d-flex align-items-center">
                {/* Vòng tròn số 2 */}
                <div
                  className="rounded-circle d-flex align-items-center justify-content-center me-3"
                  style={{
                    width: 32,
                    height: 32,
                    fontWeight: "bold",
                    border:
                      activeStep === 2
                        ? "2px solid #198754"
                        : "1px solid #dee2e6",
                    color: activeStep === 2 ? "#198754" : "inherit",
                  }}
                >
                  2
                </div>
                {/* Khối văn bản cho Step 2 */}
                <div>
                  <p
                    className={`mb-0 fw-bold ${
                      activeStep === 2 ? "text-success" : ""
                    }`}
                  >
                    Thêm thông tin tài khoản ngân hàng
                  </p>
                  <small className="text-muted">
                    Nhập đầy đủ và chính xác thông tin đăng nhập
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Wizard Content */}
        <div className="row mt-3">
          <div className="col-12">
            {/* Step 1: Danh sách ngân hàng */}
            {activeStep === 1 && (
              <div className="card">
                <div className="card-body">
                  <div className="table-responsive">
                    {loading && (
                      <p className="text-center p-3">
                        Đang tải danh sách ngân hàng...
                      </p>
                    )}
                    {error && (
                      <p className="text-center p-3 text-danger">
                        Lỗi tải dữ liệu: {error}
                      </p>
                    )}
                    {!loading && !error && (
                      <table className="table basic-border-table mb-0 table-hover">
                        <thead className="table-light">
                          <tr>
                            <th className="text-center">Ngân hàng</th>
                            <th
                              className="text-center"
                              data-bs-toggle="tooltip"
                              data-bs-placement="top"
                              data-bs-custom-class="custom-tooltip"
                              data-bs-title="Tốc độ đồng bộ giao dịch qua API từ ngân hàng sau khi thực hiện giao dịch."
                            >
                              Tốc độ{" "}
                              <Icon
                                icon="iconoir:chat-bubble-question-solid"
                                width="15"
                                height="15"
                              />
                            </th>
                            <th className="text-center">Tài khoản</th>
                            <th>Hỗ trợ</th>
                            <th className="text-center">Hành động</th>
                          </tr>
                        </thead>
                        {/* 2. SỬ DỤNG HTML GỐC CỦA BẠN ĐỂ RENDER */}
                        <tbody>
                          {activeBankList.map((bank) => (
                            <tr
                              key={bank.id}
                              className="hover-row"
                              style={{ cursor: "pointer" }}
                              onClick={() => handleConnectClick(bank)}
                            >
                              <td style={{ minWidth: "100px", width: "200px" }}>
                                <div className="d-flex align-items-center justify-content-center p-2 rounded border border-success bg-white">
                                  <img
                                    src={bank.logo}
                                    alt={bank.bankName}
                                    style={{ width: "99px" }}
                                  />
                                  {bank.isPartner && (
                                    <div
                                      className="ms-2 bank-icon-tooltip"
                                      data-bs-toggle="tooltip"
                                      data-bs-placement="top"
                                      data-bs-custom-class="custom-tooltip"
                                      data-bs-title="Ngân hàng đối tác chính thức của Pay2S"
                                    >
                                      <Icon
                                        icon="mdi:check-decagram"
                                        className="text-success"
                                        fontSize={20}
                                      />
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="text-center align-middle">
                                1-3 giây
                              </td>
                              <td className="text-center align-middle">
                                {bank.accType?.map((s, i) => (
                                  <span
                                    key={i}
                                    className={`badge me-1 ${
                                      s === "Doanh nghiệp"
                                        ? "bg-primary"
                                        : "bg-success"
                                    }`}
                                  >
                                    {s}
                                  </span>
                                ))}
                              </td>
                              <td
                                className="align-middle"
                                title={bank.bankName}
                              >
                                <ul
                                  className="list-unstyled mb-0"
                                  style={{ fontSize: "0.85rem" }}
                                >
                                  {bank.support.map((s, i) => (
                                    <li key={i}>- {s}</li>
                                  ))}
                                </ul>
                              </td>
                              <td className="text-center align-middle">
                                <button
                                  className="btn btn-success"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleConnectClick(bank);
                                  }}
                                >
                                  Kết nối
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Nhập thông tin */}
            {activeStep === 2 && selectedBank && (
              <>
                <div className="mb-3">
                  <button
                    className="btn btn-light border"
                    onClick={handleGoBack}
                  >
                    <Icon icon="ph:arrow-left-bold" className="me-1" /> Quay lại
                    chọn ngân hàng
                  </button>
                </div>
                <div className="row">
                  <div className="col-lg-6">
                    <div className="card">
                      <div className="card-body p-5">
                        <div className="d-flex align-items-center mb-4"></div>
                        <div className="mb-3">
                          <div className="btn-group">
                            <div>
                              <button
                                className={`btn btn-outline-success ${
                                  accountType === "personal" ? "active" : ""
                                }`}
                                onClick={() => setAccountType("personal")}
                              >
                                Tài khoản cá nhân
                              </button>
                            </div>
                            {/* Chỉ hiển thị nút "Tài khoản doanh nghiệp" cho các ngân hàng được chỉ định */}
                            {apiTypeSelect.includes(
                              selectedBank.shortBankName
                            ) && (
                              <div className="mx-3">
                                <button
                                  className={`btn btn-outline-success ${
                                    accountType === "business" ? "active" : ""
                                  }`}
                                  onClick={() => setAccountType("business")}
                                >
                                  Tài khoản doanh nghiệp
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="mb-20">
                          {accoutTypeSelect.includes(
                            selectedBank.shortBankName
                          ) && (
                            <label className="form-label fw-bold">
                              Chọn kiểu API
                            </label>
                          )}
                          <div className="d-flex gap-3">
                            {/* Open API */}
                            {accoutTypeSelect.includes(
                              selectedBank.shortBankName
                            ) && (
                              <div className="form-check">
                                <input
                                  className="form-check-input"
                                  type="radio"
                                  name="apiType"
                                  id="apiOpen"
                                  checked={apiType === "openapi"}
                                  onChange={() => setApiType("openapi")}
                                />
                                <label
                                  className="form-check-label"
                                  htmlFor="apiOpen"
                                >
                                  OpenAPI{" "}
                                  <small className="text-muted">
                                    (khuyên dùng)
                                  </small>
                                </label>
                              </div>
                            )}
                            {/* Pay2s API */}
                            <div className="form-check">
                              <input
                                className="form-check-input"
                                type="radio"
                                name="apiType"
                                id="apiPay2S"
                                checked={apiType === "pay2s"}
                                onChange={() => setApiType("pay2s")}
                              />
                              <label
                                className="form-check-label"
                                htmlFor="apiPay2S"
                              >
                                Pay2S API
                              </label>
                            </div>
                          </div>
                        </div>
                        <hr />
                        {renderDynamicForm()}
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-6">
                    <div className="card" style={{ height: "100%" }}>
                      <div className="card-body p-5">
                        <div className="card bg-neutral-100 p-3">
                          <p>
                            Vui lòng nhập chính xác thông tin tài khoản{" "}
                            <strong>{selectedBank.bankName}</strong>
                          </p>
                          <p>
                            Xem thêm{" "}
                            <a href="#" className="fw-bold text-success">
                              Tài liệu hướng dẫn
                            </a>
                            .
                          </p>
                          <hr />
                          <div className="pt-3">
                            <p>
                              Ngân hàng hỗ trợ tài khoản doanh nghiệp:{" "}
                              <strong>
                                ACB, Vietcombank, Vietinbank, MB Bank
                              </strong>
                            </p>
                          </div>
                          <hr />
                          <p className="pt-3" style={{ fontSize: "0.85rem" }}>
                            <span className="text-danger">Lưu ý:</span> Ngân
                            hàng kết nối qua OpenAPI có hiển thị số tài khoản ảo
                            (Virtual Account – VA), giao dịch phải qua số tài
                            khoản ảo thì Pay2S mới nhận được dữ liệu. Số tài
                            khoản ảo được tạo và hiển thị sau khi thêm tài khoản
                            thành công.
                          </p>

                          {selectedBank.shortBankName === "MBB" &&
                            accountType === "business" &&
                            apiType === "openapi" && (
                              <>
                                <hr />
                                <div className="pt-3 fw-bold text-success border-bottom pb-2 mb-2">
                                  Đối với Tài khoản doanh nghiệp MBBank sử dụng
                                  OpenAPI
                                </div>
                                <div
                                  className="mt-20 alert alert-success"
                                  style={{ fontSize: "0.85rem" }}
                                >
                                  <p className="mb-2">
                                    Quý doanh nghiệp cần ký{" "}
                                    <strong>
                                      hợp đồng 3 bên (Quý khách hàng, MB Bank và
                                      Pay2S)
                                    </strong>{" "}
                                    trong vòng 07 ngày để hoàn tất hồ sơ đăng ký
                                    sử dụng dịch vụ.
                                  </p>
                                  <p className="mb-2">
                                    Đại diện doanh nghiệp vui lòng ủy quyền,
                                    đóng dấu và gửi về địa chỉ:
                                  </p>
                                  <ul className="list-unstyled ps-3 mb-2">
                                    <li>
                                      <strong>Tên người nhận:</strong> Công ty
                                      Cổ Phần FUTE
                                    </li>
                                    <li>
                                      <strong>Địa chỉ:</strong> 15/40/30 Đường
                                      số 59, Phường 14, Quận Gò Vấp, Thành Phố
                                      Hồ Chí Minh
                                    </li>
                                    <li>
                                      <strong>Số điện thoại:</strong> 0286 270
                                      5478
                                    </li>
                                  </ul>
                                  <p className="mb-0">
                                    Quý doanh nghiệp vui lòng liên hệ với{" "}
                                    <a
                                      href="https://pay2s.vn/lien-he"
                                      target="blank"
                                    >
                                      Pay2S
                                    </a>
                                    để lấy hợp đồng và kích hoạt sử dụng.
                                  </p>
                                </div>
                              </>
                            )}
                          {selectedBank.shortBankName === "BIDV" &&
                            accountType === "personal" &&
                            apiType === "openapi" && (
                              <>
                                <hr />
                                <div className="pt-3 fw-bold text-success border-bottom pb-2 mb-2">
                                  Cấu trúc tài khoản ảo (VA) ngân hàng BIDV
                                </div>
                                <div
                                  className="mt-20 alert alert-success"
                                  style={{ fontSize: "0.85rem" }}
                                >
                                  <p className="mb-2">
                                    Cấu trúc tài khoản ảo (VA) ngân hàng BIDV
                                    dành cho cá nhân sẽ bắt đầu bằng tiền tố
                                    <strong> 963869</strong>, hậu tố bao gồm chữ
                                    cái A-Z, 0-9, độ dài VA tối đa 19 ký tự.
                                  </p>
                                </div>
                              </>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </MasterLayout>
  );
};

export default LinkBankPage;
