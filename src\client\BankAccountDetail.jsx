import React, { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, Link, useNavigate } from "react-router-dom";
import useBankApi from "../callapi/Bank.jsx";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import VAAccountsTable from "./components/VAAccountsTable";
import BankCard from "./components/BankCard";
import TransactionHistory from "./components/TransactionHistory";
import {
  AddVAModal,
  ConfirmDeleteModal,
  ConfirmOtpModal,
  QRCodeModal,
} from "./vamodals";
import { useQRPreloader } from "./vamodals/useQRPreloader";
import QRPreloader from "./vamodals/QRPreloader";
import "../assets/css/style.css";

const BankAccountDetail = () => {
  const { bankShortName, accountId: accountNumber } = useParams();
  const navigate = useNavigate();
  const [accountDetail, setAccountDetail] = useState(null);
  const [vaAccounts, setVaAccounts] = useState([]);
  const [loading, setLoading] = useState(true);

  // States for VA actions
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState(null);
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false);
  const [otpError, setOtpError] = useState("");
  const [isAddVAModalOpen, setIsAddVAModalOpen] = useState(false);
  const [isQRModalOpen, setIsQRModalOpen] = useState(false);
  const [qrAccountInfo, setQrAccountInfo] = useState(null);
  const [selectedAccountForHistory, setSelectedAccountForHistory] =
    useState(null);

  // Helper function để kiểm tra có hiển thị VA table không
  const shouldShowVATable = () => {
    // Chỉ hiển thị cho các ngân hàng hỗ trợ VA + OpenAPI, không hiển thị cho type personal
    if (!accountDetail) return false;

    // TODO: CHỖ NÀY - Thêm ngân hàng mới vào array này
    // Hiện tại: chỉ có BIDV
    // Muốn thêm ACB: ["bidv", "acb"]
    // Muốn thêm VCB: ["bidv", "acb", "vcb"]
    const supportedBanks = ["bidv", "acb"]; // Thêm ngân hàng support VA vào đây

    const isSupportedBank = supportedBanks.includes(
      accountDetail?.shortBankName?.toLowerCase()
    );

    const accountType = accountDetail?.type?.toLowerCase() || "";
    const isOpenAPI =
      accountType.includes("openapi") || accountType.includes("open");
    const isNotPersonal = accountType !== "personal";

    // Chỉ hiển thị nếu là ngân hàng được support + OpenAPI + không phải personal
    return isSupportedBank && isOpenAPI && isNotPersonal;
  };

  const {
    data: bankData,
    loading: bankLoading,
    callApi: getBankDetail,
  } = useBankApi();

  const {
    loading: actionLoading,
    error: actionError,
    callApi: performBankAction,
  } = useBankApi();

  // Preload QR cho account chính
  useQRPreloader(accountDetail);

  useEffect(() => {
    const fetchAccountDetail = async () => {
      setLoading(true);
      try {
        const userId = localStorage.getItem("user_id");
        if (userId) {
          await getBankDetail({
            action: "bank_account",
            user_id: userId,
          });
        }
      } catch (error) {
        // Error handling
      } finally {
        setLoading(false);
      }
    };

    fetchAccountDetail();
  }, [accountNumber, getBankDetail]);

  useEffect(() => {
    if (bankData && bankData.banks && bankData.banks.length > 0) {
      // Tìm bank account theo accountNumber từ danh sách banks
      const currentAccount = bankData.banks.find(
        (bank) => bank.accountNumber === accountNumber
      );

      if (currentAccount) {
        setAccountDetail(currentAccount);
      } else {
        const accountsWithSameNumber = bankData.banks.filter(
          (bank) => bank.accountNumber === accountNumber
        );
        if (accountsWithSameNumber.length > 0) {
          setAccountDetail(accountsWithSameNumber[0]);
        } else {
          setAccountDetail(bankData.banks[0]);
        }
      }

      // Set VA accounts
      const vaAccountsForThisAccount = bankData.banks.filter(
        (bank) => bank.accountNumber === accountNumber && bank.vaNumber
      );
      setVaAccounts(vaAccountsForThisAccount || []);
    } else if (bankData && bankData.account) {
      setAccountDetail(bankData.account);
      setVaAccounts(bankData.va_accounts || []);
    }
  }, [bankData, accountNumber]);

  // Tự động set tài khoản đầu tiên cho TransactionHistory khi có VA accounts
  useEffect(() => {
    if (
      shouldShowVATable() &&
      vaAccounts.length > 0 &&
      !selectedAccountForHistory
    ) {
      // Mặc định chọn tài khoản đầu tiên (có thể là main account hoặc VA đầu tiên)
      setSelectedAccountForHistory(vaAccounts[0]);
    }
  }, [vaAccounts, selectedAccountForHistory]);

  // === HANDLERS CHO VA ACTIONS ===
  const handleStatusToggle = async (vaAccount) => {
    const userId = localStorage.getItem("user_id");
    if (!userId) {
      alert("Lỗi: Thiếu User ID. Vui lòng đăng nhập lại.");
      return;
    }

    const newStatus = vaAccount.status === 1 ? 0 : 1;

    try {
      await performBankAction({
        action: "change_status",
        user_id: userId,
        bankId: vaAccount.id,
        status: newStatus,
      });

      setVaAccounts((currentVAs) =>
        currentVAs.map((va) =>
          va.id === vaAccount.id
            ? {
                ...va,
                status: newStatus,
                statusText: newStatus === 1 ? "Hoạt động" : "Không hoạt động",
              }
            : va
        )
      );
    } catch (error) {
      alert("Cập nhật trạng thái thất bại!");
    }
  };

  const handleDeleteClick = (vaAccount) => {
    setAccountToDelete(vaAccount);
    setIsConfirmModalOpen(true);
  };

  const handleQRClick = (vaAccount) => {
    setQrAccountInfo(vaAccount);
    setIsQRModalOpen(true);
  };

  const handleViewHistory = (vaAccount) => {
    setSelectedAccountForHistory(vaAccount);
    // Scroll to TransactionHistory section
    setTimeout(() => {
      const historyElement = document.getElementById(
        "transaction-history-section"
      );
      if (historyElement) {
        historyElement.scrollIntoView({ behavior: "smooth" });
      }
    }, 100);
  };

  const handleConfirmDelete = async () => {
    if (!accountToDelete) {
      return;
    }

    const userId = localStorage.getItem("user_id");
    const bankId = accountToDelete.id;

    if (!userId || !bankId) {
      alert(
        "Lỗi: Thiếu User ID hoặc Bank ID. Vui lòng đăng nhập lại và thử lại."
      );
      setIsConfirmModalOpen(false);
      return;
    }

    if (
      accountToDelete.shortBankName === "MBB" ||
      accountDetail?.shortBankName === "MBB"
    ) {
      try {
        const result = await performBankAction({
          action: "delete",
          user_id: userId,
          bankId: bankId,
        });

        setIsConfirmModalOpen(false);
        setIsOtpModalOpen(true);
      } catch (error) {
        alert("Gửi yêu cầu OTP thất bại!");
      }
    } else {
      try {
        const deletePayload = {
          action: "delete",
          user_id: userId,
          bankId: bankId,
        };

        const result = await performBankAction(deletePayload);

        setIsConfirmModalOpen(false);

        setVaAccounts((currentVAs) => {
          const updatedVAs = currentVAs.filter((va) => va.id !== bankId);
          // Nếu xóa hết VA, quay về trang danh sách
          if (shouldShowVATable() && updatedVAs.length === 0) {
            navigate(`/client/account-bank/${bankShortName}`);
          }
          return updatedVAs;
        });

        const currentUserId = localStorage.getItem("user_id");
        if (currentUserId) {
          await getBankDetail({
            action: "bank_account",
            user_id: currentUserId,
          });
        }
      } catch (error) {
        alert("Xóa tài khoản ảo thất bại!");
      }
    }
  };

  const handleSubmitOtp = async (otp) => {
    setOtpError("");
    const userId = localStorage.getItem("user_id");
    const bankId = accountToDelete.id;

    try {
      const otpPayload = {
        action: "confirm_otp_delete",
        user_id: userId,
        bankId: bankId,
        otp: otp,
        ip: "",
      };

      const result = await performBankAction(otpPayload);

      setIsOtpModalOpen(false);

      setVaAccounts((currentVAs) => {
        const updatedVAs = currentVAs.filter((va) => va.id !== bankId);
        // Nếu xóa hết VA, quay về trang danh sách
        if (shouldShowVATable() && updatedVAs.length === 0) {
          navigate(`/client/account-bank/${bankShortName}`);
        }
        return updatedVAs;
      });

      const currentUserId = localStorage.getItem("user_id");
      if (currentUserId) {
        await getBankDetail({
          action: "bank_account",
          user_id: currentUserId,
        });
      }
    } catch (err) {
      setOtpError(err.message || "Xác nhận OTP thất bại");
    }
  };

  const formatCurrency = (amount) =>
    new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount || 0);

  if (loading || bankLoading) {
    return (
      <MasterLayout>
        <div className="d-flex justify-content-center align-items-center min-vh-25">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="mt-3">Đang tải thông tin tài khoản...</p>
          </div>
        </div>
      </MasterLayout>
    );
  }

  return (
    <MasterLayout>
      <Breadcrumb
        title={`Chi tiết tài khoản ${
          accountDetail?.bankName ||
          accountDetail?.shortBankName ||
          bankShortName
        }`}
        items={[
          { label: "Ngân hàng", href: "/client/banklist" },
          {
            label: `Danh sách ${
              accountDetail?.shortBankName ||
              accountDetail?.bankName ||
              bankShortName
            }`,
            href: `/client/account-bank/${bankShortName}`,
          },
          { label: "Chi tiết tài khoản" },
        ]}
      />

      <div className="container-fluid mt-3">
        {actionError && (
          <div className="alert alert-danger">
            Thao tác thất bại: {actionError}
          </div>
        )}

        <div className="row g-3 align-items-end">
          <div className="col-lg-4 col-md-6 col-sm-12">
            <BankCard
              accountDetail={accountDetail}
              formatCurrency={formatCurrency}
            />
          </div>

          {/* Nút tạo QR cho ngân hàng không có VA */}
          {!shouldShowVATable() && accountDetail && (
            <div className="col-auto">
              <button
                className="btn btn-primary btn-lg"
                onClick={() => handleQRClick(accountDetail)}
                style={{
                  minWidth: "180px",
                  fontSize: "1.1rem",
                  fontWeight: "600",
                  borderRadius: "8px",
                  transition: "all 0.3s ease",
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = "translateY(-2px)";
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = "translateY(0)";
                }}
              >
                <Icon
                  icon="ph:qr-code"
                  className="me-2"
                  style={{ fontSize: "1.3rem" }}
                />
                Tạo mã QR
              </button>
            </div>
          )}
        </div>

        {shouldShowVATable() && (
          <VAAccountsTable
            vaAccounts={vaAccounts}
            actionLoading={actionLoading}
            onStatusToggle={handleStatusToggle}
            onQRClick={handleQRClick}
            onDeleteClick={handleDeleteClick}
            onAddVA={() => setIsAddVAModalOpen(true)}
            onViewHistory={handleViewHistory}
            formatCurrency={formatCurrency}
          />
        )}

        {/* Transaction History */}
      </div>
      <div className="my-5" id="transaction-history-section">
        <TransactionHistory
          accountDetail={accountDetail}
          selectedAccount={selectedAccountForHistory}
        />
      </div>

      {/* Preload QR cho tất cả VA accounts */}
      {vaAccounts.map((vaAccount, index) => (
        <QRPreloader
          key={`qr-preload-${vaAccount.id || index}`}
          account={vaAccount}
        />
      ))}

      {/* Modal thêm VA */}
      <AddVAModal
        isOpen={isAddVAModalOpen}
        onClose={() => setIsAddVAModalOpen(false)}
        accountNumber={accountNumber}
        bankShortName={bankShortName}
      />

      {/* Modal xác nhận xóa VA */}
      <ConfirmDeleteModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmDelete}
        accountName={`${
          accountToDelete?.vaNumber || accountToDelete?.accountNumber || "VA"
        }`}
        loading={actionLoading}
      />

      {/* Modal nhập OTP */}
      <ConfirmOtpModal
        isOpen={isOtpModalOpen}
        onClose={() => setIsOtpModalOpen(false)}
        onSubmit={handleSubmitOtp}
        loading={actionLoading}
        error={otpError}
      />

      {/* Modal QR Code */}
      <QRCodeModal
        isOpen={isQRModalOpen}
        onClose={() => setIsQRModalOpen(false)}
        accountInfo={qrAccountInfo}
      />
    </MasterLayout>
  );
};

export default BankAccountDetail;
