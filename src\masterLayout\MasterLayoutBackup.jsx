/* eslint-disable react/prop-types */
import React, {
  Fragment,
  useEffect,
  useState,
  useRef,
  useCallback,
} from "react";
// Bỏ import axios trực tiếp
import { Icon } from "@iconify/react/dist/iconify.js";
import { Link, NavLink, useLocation } from "react-router-dom";
import { Dropdown } from "bootstrap";
import { io } from "socket.io-client";

// 1. Import các custom hook
import useUserApi from "../callapi/User";
import useSystemApi from "../callapi/System";

import { menuConfig } from "./MenuConfig";
import logo from "../assets/images/logo.png";
import logoLight from "../assets/images/logo-light.png";
import logoIcon from "../assets/images/logo-icon.png";
import ProfileIcon from "../assets/images/profile.png";

// =================================================================
// START: COMPONENT NOTIFICATIONS ĐÃ ĐƯỢC REFACTOR VỚI HOOK
// =================================================================
const Notifications = () => {
  const [notifications, setNotifications] = useState([]);
  const socketRef = useRef(null);
  const dropdownToggleRef = useRef(null);
  const dropdownInstanceRef = useRef(null);

  const token = localStorage.getItem("token");
  const userId = localStorage.getItem("user_id");

  // 2. Sử dụng các hook cho từng mục đích
  const {
    data: initialNotiData,
    loading: initialLoading,
    callApi: fetchInitialNotificationsApi,
  } = useSystemApi();
  const { callApi: markAsReadApi } = useUserApi(); // Chỉ cần hàm callApi

  // 3. useEffect gọi API lấy danh sách ban đầu thông qua hook
  useEffect(() => {
    if (userId) {
      fetchInitialNotificationsApi({ action: "user_noti", user_id: userId });
    }
  }, [userId, fetchInitialNotificationsApi]);

  // 4. useEffect xử lý dữ liệu trả về từ hook
  useEffect(() => {
    if (initialNotiData?.status) {
      // THAY ĐỔI 1: Sắp xếp thông báo từ API, mới nhất lên đầu
      const sortedNotifications = (initialNotiData.notification || []).sort(
        (a, b) => new Date(b.created_at) - new Date(a.created_at)
      );
      setNotifications(sortedNotifications);
    }
  }, [initialNotiData]);

  // 5. useEffect cho WebSocket không thay đổi, vẫn hoạt động song song
  useEffect(() => {
    if (!token || !userId) return;
    const socket = io("http://103.249.116.179:4004", {
      transports: ["websocket"],
    });
    socketRef.current = socket;
    socket.on("connect", () => {
      if (userId) socket.emit("register", userId);
    });
    socket.on("new_notification", (newNotification) => {
      // Logic này đã đúng: thêm thông báo mới vào đầu mảng
      setNotifications((prev) => [newNotification, ...prev]);
      if (dropdownInstanceRef.current) dropdownInstanceRef.current.show();
    });
    return () => socket.disconnect();
  }, [userId, token]);

  const unreadCount = notifications.filter((n) => n.is_read === 0).length;

  // 6. Hàm markAsRead giờ đây gọi hàm từ hook userApi
  const handleMarkAsRead = (notification) => {
    if (notification.is_read === 0) {
      setNotifications((prev) =>
        prev.map((n) => (n.id === notification.id ? { ...n, is_read: 1 } : n))
      );
      markAsReadApi({
        action: "mark_noti_read",
        user_id: userId,
        id: notification.id,
      });
    }
  };

  return (
    <>
      <button
        ref={dropdownToggleRef}
        className="has-indicator w-40-px h-40-px bg-neutral-200 rounded-circle d-flex justify-content-center align-items-center position-relative"
        type="button"
        data-bs-toggle="dropdown"
        aria-expanded="false"
        data-bs-auto-close="outside"
      >
        <Icon icon="iconoir:bell" className="text-primary-light text-xl" />
        {/* THAY ĐỔI 2: Hiển thị số lượng thông báo chưa đọc trên badge */}
        {unreadCount > 0 && (
          <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
            {unreadCount}
          </span>
        )}
      </button>

      <div className="dropdown-menu to-top dropdown-menu-lg p-0">
        <div className="m-16 py-12 px-16 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
          <h6 className="text-lg text-primary-light fw-semibold mb-0">
            Thông báo
          </h6>
          {unreadCount > 0 && (
            <span className="text-primary-600 fw-semibold text-lg w-40-px h-40-px rounded-circle bg-base d-flex justify-content-center align-items-center">
              {unreadCount}
            </span>
          )}
        </div>
        <div className="max-h-400-px overflow-y-auto scroll-sm pe-4">
          {initialLoading ? (
            <div className="p-5 text-center">Đang tải...</div>
          ) : notifications.length > 0 ? (
            notifications.map((n) => (
              <Link
                key={n.id}
                to={n.link_to_screen || "#"}
                onClick={() => handleMarkAsRead(n)}
                className={`px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between notification-item ${
                  n.is_read === 0 ? "unread" : ""
                }`}
              >
                <div className="d-flex align-items-center gap-3">
                  <span
                    className={`w-44-px h-44-px rounded-circle d-flex justify-content-center align-items-center flex-shrink-0 ${
                      n.is_read === 0
                        ? "bg-success-subtle text-success-main"
                        : "bg-neutral-200 text-secondary-light"
                    }`}
                  >
                    <Icon icon="iconoir:bell" className="icon text-xxl" />
                  </span>
                  <div>
                    <h6
                      className={`text-md fw-semibold mb-4 ${
                        n.is_read === 1 ? "text-secondary-light" : ""
                      }`}
                    >
                      {n.title || "Thông báo hệ thống"}
                    </h6>
                    <p className="mb-0 text-sm text-secondary-light">
                      {n.content}
                    </p>
                    <span className="text-sm text-secondary-light flex-shrink-0">
                      {new Date(n.created_at || Date.now()).toLocaleString(
                        "vi-VN"
                      )}
                    </span>
                  </div>
                </div>
                {n.is_read === 0 && <div className="unread-indicator"></div>}
              </Link>
            ))
          ) : (
            <div className="p-3 text-center">Không có thông báo nào.</div>
          )}
        </div>
        <div className="text-center py-12 px-16">
          <Link
            to="https://pay2s.vn/blog/thong-bao/"
            className="text-primary-600 fw-semibold text-md"
            target="_blank"
            rel="noopener noreferrer"
          >
            Xem tất cả
          </Link>
        </div>
      </div>
    </>
  );
};
// =================================================================
// END: COMPONENT NOTIFICATIONS
// =================================================================

const MasterLayout = ({ children }) => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [mobileMenu, setMobileMenu] = useState(false);
  const location = useLocation();
  const [profile, setProfile] = useState({
    firstname: "",
    lastname: "",
    current_plan: null,
  });

  const [openSidebarMenu, setOpenSidebarMenu] = useState(null);

  const handleSidebarMenuToggle = (menuLabel) => {
    setOpenSidebarMenu((prevMenu) =>
      prevMenu === menuLabel ? null : menuLabel
    );
  };

  useEffect(() => {
    const token = localStorage.getItem("token");
    const userId = localStorage.getItem("user_id");
    if (!token || !userId) return;
    const fetchProfile = async () => {
      try {
        const axios = (await import("axios")).default;
        const res = await axios.post(
          "https://api.pay2s.vn/api/v1/user",
          new URLSearchParams({ action: "get_profile", user_id: userId }),
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/x-www-form-urlencoded",
            },
          }
        );
        if (res.data.status) {
          setProfile(res.data.message);
        }
      } catch {}
    };
    fetchProfile();
  }, []);

  useEffect(() => {
    const dropdownToggleList = Array.from(
      document.querySelectorAll('[data-bs-toggle="dropdown"]')
    );
    const dropdownList = dropdownToggleList.map((el) => new Dropdown(el));
    return () => {
      dropdownList.forEach((dropdown) => dropdown.dispose());
    };
  }, []);

  useEffect(() => {
    let activeParentLabel = null;
    menuConfig.forEach((group) => {
      group.items.forEach((item) => {
        if (
          item.dropdown &&
          item.dropdown.some((subItem) => subItem.to === location.pathname)
        ) {
          activeParentLabel = item.label;
        }
      });
    });
    setOpenSidebarMenu(activeParentLabel);
  }, [location.pathname]);

  const sidebarControl = () => setSidebarActive((prev) => !prev);
  const mobileMenuControl = () => setMobileMenu((prev) => !prev);

  const storedUsername = localStorage.getItem("username") || "";
  const displayName =
    profile.firstname && profile.lastname
      ? `${profile.firstname} ${profile.lastname}`
      : storedUsername;
  const displayPlan = profile.current_plan || "Người dùng mới";

  return (
    <section className={mobileMenu ? "overlay active" : "overlay"}>
      <aside
        className={
          sidebarActive
            ? "sidebar active"
            : mobileMenu
            ? "sidebar sidebar-open"
            : "sidebar"
        }
      >
        <button
          onClick={mobileMenuControl}
          className="sidebar-close-btn"
          type="button"
        >
          <Icon icon="radix-icons:cross-2" />
        </button>
        <div>
          <Link to="/" className="sidebar-logo">
            <img src={logo} alt="Pay2S logo" className="light-logo" />
            <img src={logoLight} alt="Pay2S logo light" className="dark-logo" />
            <img src={logoIcon} alt="Pay2S logo icon" className="logo-icon" />
          </Link>
        </div>
        <div className="sidebar-menu-area">
          <ul className="sidebar-menu" id="sidebar-menu">
            {menuConfig.map((group, i) => (
              <Fragment key={i}>
                {group.label && (
                  <li className="sidebar-menu-group-title">{group.label}</li>
                )}
                {group.items.map((item, j) =>
                  item.dropdown ? (
                    <li
                      className={`dropdown ${
                        openSidebarMenu === item.label ? "open" : ""
                      }`}
                      key={j}
                    >
                      <Link
                        to="#"
                        onClick={(e) => {
                          e.preventDefault();
                          handleSidebarMenuToggle(item.label);
                        }}
                      >
                        <Icon icon={item.icon} className="menu-icon" />
                        <span>{item.label}</span>
                      </Link>
                      <ul className="sidebar-submenu">
                        {item.dropdown.map((sub, k) => (
                          <li key={k}>
                            <NavLink
                              to={sub.to}
                              className={(nav) =>
                                nav.isActive ? "active-page" : ""
                              }
                            >
                              <i
                                className={`ri-circle-fill circle-icon text-${sub.color} w-auto`}
                              />
                              {sub.label}
                            </NavLink>
                          </li>
                        ))}
                      </ul>
                    </li>
                  ) : (
                    <li key={j}>
                      <NavLink
                        to={item.to}
                        className={(nav) => (nav.isActive ? "active-page" : "")}
                      >
                        <Icon icon={item.icon} className="menu-icon" />
                        <span>{item.label}</span>
                      </NavLink>
                    </li>
                  )
                )}
              </Fragment>
            ))}
          </ul>
        </div>
      </aside>
      <main
        className={sidebarActive ? "dashboard-main active" : "dashboard-main"}
      >
        <div className="navbar-header">
          <div className="row align-items-center justify-content-between">
            <div className="col-auto">
              <div className="d-flex align-items-center gap-4">
                <button
                  className="sidebar-toggle"
                  type="button"
                  onClick={sidebarControl}
                >
                  {sidebarActive ? (
                    <Icon
                      icon="iconoir:arrow-right"
                      className="icon text-2xl non-active"
                    />
                  ) : (
                    <Icon
                      icon="heroicons:bars-3-solid"
                      className="icon text-2xl non-active"
                    />
                  )}
                </button>
                <button
                  className="sidebar-mobile-toggle"
                  type="button"
                  onClick={mobileMenuControl}
                >
                  <Icon icon="heroicons:bars-3-solid" className="icon" />
                </button>
                <form className="navbar-search">
                  <input type="text" name="search" placeholder="Search" />
                  <Icon icon="ion:search-outline" className="icon" />
                </form>
              </div>
            </div>
            <div className="col-auto">
              <div className="d-flex align-items-center gap-3">
                <div className="dropdown notification-dropdown">
                  <Notifications />
                </div>
                <div className="dropdown user-button">
                  <button
                    className="d-flex justify-content-center align-items-center rounded-circle border-0 p-0"
                    type="button"
                    data-bs-toggle="dropdown"
                    aria-expanded="false"
                  >
                    <img
                      src={ProfileIcon}
                      alt="User Avatar"
                      className="w-40-px h-40-px object-fit-cover rounded-circle"
                    />
                  </button>
                  <div className="dropdown-menu to-top dropdown-menu-sm">
                    <div className="py-12 px-16 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
                      <div>
                        <h6 className="text-lg text-primary-light fw-semibold mb-2">
                          {displayName}
                        </h6>
                        <span className="text-secondary-light fw-medium text-sm">
                          {displayPlan}
                        </span>
                      </div>
                    </div>
                    <ul className="list-unstyled px-16">
                      <li>
                        <Link
                          to="/client/profile"
                          className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-primary d-flex align-items-center gap-3"
                        >
                          <Icon
                            icon="solar:user-linear"
                            className="icon text-xl"
                          />{" "}
                          Hồ sơ
                        </Link>
                      </li>
                      <li>
                        <Link
                          to="/client/invoice"
                          className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-primary d-flex align-items-center gap-3"
                        >
                          <Icon
                            icon="icon-park-outline:setting-two"
                            className="icon text-xl"
                          />{" "}
                          Hóa đơn
                        </Link>
                      </li>
                      <li>
                        <button
                          className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-danger d-flex align-items-center gap-3 bg-transparent border-0 w-100 text-start"
                          onClick={() => {
                            ["token", "user_id", "username"].forEach((k) =>
                              localStorage.removeItem(k)
                            );
                            window.location.href = "/client/login";
                          }}
                        >
                          <Icon icon="lucide:power" className="icon text-xl" />{" "}
                          Đăng xuất
                        </button>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="dashboard-main-body">{children}</div>
        <footer className="d-footer">
          <div className="row align-items-center justify-content-between">
            <div className="col-auto">
              <p className="mb-0">
                © 2025 - <span className="text-primary-600">Pay2S</span> - API
                ngân hàng và Ví điện tử tốc độ nhanh, bảo mật, giao dịch không
                giới hạn
              </p>
            </div>
          </div>
        </footer>
      </main>
    </section>
  );
};

export default MasterLayout;
