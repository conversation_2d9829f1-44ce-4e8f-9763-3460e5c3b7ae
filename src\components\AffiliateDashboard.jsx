import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import useBootstrapTooltip from "../hook/useBootstrapTooltip.js";

const AffiliateDashboard = ({ report = {}, userList = [] }) => {
  useBootstrapTooltip([AffiliateDashboard]);
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [copied, setCopied] = useState(false);
  const itemsPerPage = 10;
  const totalPages = Math.ceil(userList.length / itemsPerPage);
  const paginatedUsers = userList.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  // Calculate display range
  const startIdx =
    userList.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
  const endIdx = Math.min(currentPage * itemsPerPage, userList.length);

  // Tạo mảng chỉ số từ report
  const stats = [
    {
      icon: "mdi:cursor-default-click-outline",
      value: report.affiliate_hit || 0,
      label: "L<PERSON>ợt nhấp",
      tooltip: "Tổng số lượt nhấp chuột đến liên kết của bạn",
    },
    {
      icon: "mdi:account-group-outline",
      value: userList.length,
      label: "Đã đăng ký",
      tooltip: "Số người đã đăng ký qua liên kết của bạn",
    },
    {
      icon: "mdi:clock-outline",
      value: (report.pending_balance || 0).toLocaleString("vi-VN") + " ₫",
      label: "Số dư chờ rút",
      tooltip: "Số dư đang chờ bạn rút",
    },
    {
      icon: "mdi:cash-multiple",
      value: (report.paid_balance || 0).toLocaleString("vi-VN") + " ₫",
      label: "Số dư đã rút",
      tooltip: "Tổng số tiền bạn đã rút thành công",
    },
    {
      icon: "mdi:hand-coin-outline",
      value: report.affiliate_pending || 0,
      label: "Đơn chờ duyệt",
      tooltip: "Số đơn đang chờ xét duyệt để tính hoa hồng",
    },
    {
      icon: "mdi:cash-check",
      value: report.affiliate_paid || 0,
      label: "Đơn đã Thanh toán",
      tooltip: "Số đơn đã được thanh toán hoa hồng",
    },
  ];

  // Export userList to CSV
  const handleExportExcel = () => {
    const headers = ["ID", "Username", "Email", "Created At"];
    const rows = userList.map((u) => [u.id, u.username, u.email, u.created_at]);
    const csvContent = [headers, ...rows].map((e) => e.join(",")).join("\n");
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "affiliate_users.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Print current page
  const handlePrint = () => {
    window.print();
  };

  // Copy link with fallback for older browsers
  const fallbackCopyText = (text) => {
    const textarea = document.createElement("textarea");
    textarea.value = text;
    textarea.setAttribute("readonly", "");
    textarea.style.position = "absolute";
    textarea.style.left = "-9999px";
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand("copy");
      setCopied(true);
    } catch (err) {
      console.error("Fallback: unable to copy", err);
    }
    document.body.removeChild(textarea);
  };

  const handleCopyLink = () => {
    const text = report.ref_link || "";
    if (!text) {
      return;
    }
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          setCopied(true);
        })
        .catch((err) => {
          console.error("Navigator: unable to copy", err);
          fallbackCopyText(text);
        });
    } else {
      fallbackCopyText(text);
    }
    setTimeout(() => setCopied(false), 500);
  };

  return (
    <>
      {/* Phần Liên kết tiếp thị */}
      <div className="card mb-3 mt-3">
        <div className="card-body">
          <div className="row align-items-center">
            <div className="col-md-8">
              <label htmlFor="affiliateLink" className="form-label fw-semibold">
                Liên kết tiếp thị của bạn
              </label>
              <div className="input-group d-flex align-items-center">
                <input
                  type="text"
                  id="affiliateLink"
                  className="form-control rounded"
                  value={report.ref_link || ""}
                  readOnly
                />
                <button
                  className="btn rounded text-bg-success mx-10"
                  type="button"
                  title="Sao chép"
                  onClick={handleCopyLink}
                >
                  <Icon icon="mdi:content-copy" />
                </button>
                <span
                  className="text-success text-sm position-absolute end-0"
                  style={{
                    top: "-30px",
                    opacity: copied ? 1 : 0,
                    transition: "opacity 0.3s",
                  }}
                >
                  Đã sao chép!
                </span>
              </div>
            </div>
            <div className="col-md-4 text-md-end mt-3 mt-md-0">
              <div className="money-income d-flex p-1 align-items-center gap-3 justify-content-end ">
                <div>
                  <span className="text-muted d-block">Số dư</span>
                  <span className="fw-bold fs-5 ">
                    {(report.available_balance || 0).toLocaleString("vi-VN")} ₫
                  </span>
                </div>
                <div className="border bg-neutral-200 rounded p-10 h-100">
                  <Icon
                    icon="streamline-ultimate:accounting-coins-bold"
                    width={"25px"}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Phần Chỉ số */}
      <div className="card mb-4">
        <div className="card-body">
          <div className="d-flex justify-content-between align-items-center mb-3">
            <h5 className="card-title mb-0">Chỉ số</h5>
            <span className="text-muted">Tỷ lệ hoa hồng: 10%</span>
          </div>
          <div className="row g-3">
            {stats.map((stat, index) => (
              <div className="col-lg-3 col-md-6" key={index}>
                <div
                  className="text-muted small"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  data-bs-custom-class="custom-tooltip"
                  data-bs-title={stat.tooltip}
                >
                  <div className="d-flex align-items-center p-3 border rounded">
                    <div className="me-3 ">
                      <Icon
                        icon={stat.icon}
                        fontSize={32}
                        className="text-muted"
                      />
                    </div>
                    <div>
                      <div className="fs-4 fw-bold">{stat.value}</div>
                      {stat.label}{" "}
                      <Icon icon="mdi:information-outline" className="ms-1" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Phần Danh sách người mua */}
      <div className="card mt-3">
        <div className="card-body">
          <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 gap-2">
            <h5 className="card-title mb-0">Danh sách người mua</h5>
            <div className="d-flex align-items-center gap-2">
              <input
                type="search"
                className="form-control form-control-sm"
                placeholder="Tìm kiếm..."
              />
              <div className="d-flex">
                <button
                  className="btn btn-success btn-sm"
                  onClick={handleExportExcel}
                  disabled={userList.length === 0}
                >
                  Excel
                </button>
                <button
                  className="btn btn-success btn-sm ms-2"
                  onClick={handlePrint}
                  disabled={userList.length === 0}
                >
                  Print
                </button>
              </div>
            </div>
          </div>
          <div className="table-responsive">
            <table className="table bordered-table mb-0">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Tài khoản</th>
                  <th>Email</th>
                  <th>Thời gian</th>
                </tr>
              </thead>
              <tbody>
                {userList.length > 0 ? (
                  paginatedUsers.map((u) => (
                    <tr key={u.id}>
                      <td>{u.id}</td>
                      <td>{u.username}</td>
                      <td>{u.email}</td>
                      <td>{new Date(u.created_at).toLocaleString()}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="4" className="text-center p-4">
                      Không có dữ liệu giao dịch
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className="d-flex justify-content-between align-items-center mt-3">
            <span className="text-muted small">
              {userList.length > 0
                ? `Hiển thị ${startIdx}-${endIdx}/${userList.length}`
                : "Không có dữ liệu để hiển thị"}
            </span>
            <nav aria-label="pagination">
              <ul className="pagination pagination-sm mb-0">
                <li
                  className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
                >
                  <a
                    className="page-link"
                    href="#"
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                  >
                    &lt;
                  </a>
                </li>
                {[...Array(totalPages)].map((_, i) => (
                  <li
                    className={`page-item ${
                      currentPage === i + 1 ? "active" : ""
                    }`}
                    key={i}
                  >
                    <a
                      className="page-link"
                      href="#"
                      onClick={() => setCurrentPage(i + 1)}
                    >
                      {i + 1}
                    </a>
                  </li>
                ))}
                <li
                  className={`page-item ${
                    currentPage === totalPages ? "disabled" : ""
                  }`}
                >
                  <a
                    className="page-link"
                    href="#"
                    onClick={() =>
                      setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                    }
                  >
                    &gt;
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </>
  );
};

export default AffiliateDashboard;
