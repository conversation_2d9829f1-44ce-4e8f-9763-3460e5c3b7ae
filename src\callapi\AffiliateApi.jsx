import { useState, useCallback } from "react";
import axios from "axios";
import { API_BASE_URL } from "./apiConfig";

// Hook dành riêng cho các API liên quan đến Affiliate
const useAffiliateApi = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const callApi = useCallback(async (body) => {
    setLoading(true);
    setError(null);
    setData(null);

    const API_ENDPOINT = `${API_BASE_URL}/affiliate`;

    try {
      const token = localStorage.getItem("token");
      const headers = { "Content-Type": "application/x-www-form-urlencoded" };

      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      } else {
        throw new Error("Không tìm thấy token xác thực. Vui lòng đăng nhập.");
      }

      const requestBody = new URLSearchParams(body);
      const response = await axios.post(API_ENDPOINT, requestBody, { headers });
      const result = response.data;

      if (result.status === true) {
        setData(result);
        return result;
      } else {
        setError(result.message || "API trả về lỗi không xác định.");
        return null;
      }
    } catch (err) {
      const errorMessage = err.response
        ? err.response.data.message || err.response.data.error || err.message
        : err.message;
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  return { data, loading, error, callApi };
};

export default useAffiliateApi;
