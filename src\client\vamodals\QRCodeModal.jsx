import React, { useState, useEffect, useRef } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { qrImageCache } from "./useQRPreloader";

const QRCodeModal = ({ isOpen, onClose, accountInfo }) => {
  const [qrUrl, setQrUrl] = useState("");
  const [qrImageUrl, setQrImageUrl] = useState("");
  const [isScanning, setIsScanning] = useState(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    if (isOpen && accountInfo) {
      setIsScanning(true);
      setQrImageUrl("");
      generateQRUrl();
    }

    // Cleanup khi component unmount hoặc modal đóng
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [isOpen, accountInfo]);

  const generateQRUrl = async () => {
    if (!accountInfo) return;

    const baseUrl = "https://payment.pay2s.vn/quicklink";
    const bankName = accountInfo.shortBankName || "BIDV";
    const accountNumber =
      accountInfo.vaNumber || accountInfo.accountNumber || "";
    const accountName = encodeURIComponent(
      accountInfo.name || accountInfo.username || ""
    );

    const params = new URLSearchParams({
      amount: "",
      memo: "",
      is_mask: "0",
      bg: "0",
    });

    const url = `${baseUrl}/${bankName}/${accountNumber}/${accountName}?${params.toString()}`;
    setQrUrl(url);

    // Tạo cache key
    const cacheKey = `${bankName}-${accountNumber}-${accountName}`;

    // Kiểm tra cache trước
    if (qrImageCache.has(cacheKey)) {
      // Simulate HUD scan delay for cache hit
      setTimeout(() => {
        setQrImageUrl(qrImageCache.get(cacheKey));
        setIsScanning(false);
      }, 1000);
      return;
    }

    // Hủy request cũ nếu có
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    // Danh sách URLs từ Pay2S để thử song song
    const imageUrls = [
      `${url}&format=png`,
      `${url}&format=image`,
      `${baseUrl}/image/${bankName}/${accountNumber}/${accountName}?${params.toString()}`,
      `${url}&export=image`,
      `${url}&download=qr`,
      `${url}&type=qr`,
      `${url}.png`,
      `${url}.jpg`,
      `${url}&output=image`,
      `${url}&qr=image`,
    ];

    try {
      const workingUrl = await findWorkingImageUrl(
        imageUrls,
        abortControllerRef.current.signal
      );
      if (workingUrl && !abortControllerRef.current.signal.aborted) {
        setQrImageUrl(workingUrl);
        qrImageCache.set(cacheKey, workingUrl); // Cache kết quả
        setIsScanning(false);
      }
    } catch (error) {
      if (!abortControllerRef.current.signal.aborted) {
        console.error("All QR image URLs failed:", error);
        setQrImageUrl("");
        setIsScanning(false);
      }
    }
  };

  // Hàm tìm URL hoạt động bằng Promise.any
  const findWorkingImageUrl = async (urls, signal) => {
    const testPromises = urls.map(async (url) => {
      return new Promise((resolve, reject) => {
        if (signal.aborted) {
          reject(new Error("Aborted"));
          return;
        }

        const img = new Image();
        const timeout = setTimeout(() => {
          reject(new Error(`Timeout for ${url}`));
        }, 3000); // 3s timeout cho mỗi URL

        img.onload = () => {
          clearTimeout(timeout);
          resolve(url);
        };

        img.onerror = () => {
          clearTimeout(timeout);
          reject(new Error(`Failed to load ${url}`));
        };

        // Kiểm tra abort signal
        if (signal.aborted) {
          clearTimeout(timeout);
          reject(new Error("Aborted"));
          return;
        }

        img.src = url;
      });
    });

    // Trả về URL đầu tiên load thành công
    return Promise.any(testPromises);
  };

  const downloadQR = () => {
    if (!qrImageUrl) return;

    const link = document.createElement("a");
    link.href = qrImageUrl;
    link.download = `QR-${accountInfo?.shortBankName}-${
      accountInfo?.vaNumber || accountInfo?.accountNumber
    }.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const openQRLink = () => {
    if (qrUrl) {
      window.open(qrUrl, "_blank");
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="modal fade show"
      style={{ display: "block", backgroundColor: "rgba(0,0,0,0.5)" }}
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-body text-center">
            {/* QR Code Image với HUD Scan Animation */}
            <div className="mb-4">
              <div
                className="qr-container position-relative d-inline-block"
                style={{
                  maxWidth: "100%",
                  overflow: "hidden",
                }}
              >
                {/* QR Image */}
                {qrImageUrl && (
                  <img
                    src={qrImageUrl}
                    alt="QR Code"
                    className={`${isScanning ? "qr-scanning" : "qr-ready"}`}
                    style={{
                      maxWidth: "100%",
                      height: "auto",
                      opacity: isScanning ? 0.3 : 1,
                      transition: "opacity 0.5s ease",
                    }}
                    onLoad={() => {
                      console.log("QR Image loaded successfully:", qrImageUrl);
                    }}
                  />
                )}

                {/* HUD Scan Animation - chỉ hiện khi scanning */}
                {isScanning && qrImageUrl && (
                  <>
                    {/* Scanning Line */}
                    <div
                      className="scan-line position-absolute"
                      style={{
                        width: "100%",
                        height: "2px",
                        background:
                          "linear-gradient(90deg, transparent, #198754, transparent)",
                        left: "0",
                        animation: "scanDown 2s ease-in-out infinite",
                        boxShadow: "0 0 10px #198754",
                        zIndex: 10,
                      }}
                    />

                    {/* Corner brackets */}
                    <div
                      className="position-absolute"
                      style={{
                        top: "10px",
                        left: "10px",
                        width: "20px",
                        height: "20px",
                        borderTop: "3px solid #198754",
                        borderLeft: "3px solid #198754",
                      }}
                    />
                    <div
                      className="position-absolute"
                      style={{
                        top: "10px",
                        right: "10px",
                        width: "20px",
                        height: "20px",
                        borderTop: "3px solid #198754",
                        borderRight: "3px solid #198754",
                      }}
                    />
                    <div
                      className="position-absolute"
                      style={{
                        bottom: "10px",
                        left: "10px",
                        width: "20px",
                        height: "20px",
                        borderBottom: "3px solid #198754",
                        borderLeft: "3px solid #198754",
                      }}
                    />
                    <div
                      className="position-absolute"
                      style={{
                        bottom: "10px",
                        right: "10px",
                        width: "20px",
                        height: "20px",
                        borderBottom: "3px solid #198754",
                        borderRight: "3px solid #198754",
                      }}
                    />

                    {/* Scanning text */}
                    <div
                      className="position-absolute w-100 text-center"
                      style={{
                        bottom: "20px",
                        color: "#198754",
                        fontSize: "14px",
                        fontWeight: "bold",
                        textShadow: "0 0 8px #198754",
                        animation:
                          "scanPulse 1s ease-in-out infinite alternate",
                      }}
                    >
                      ĐANG TẠO QR CODE...
                    </div>
                  </>
                )}

                {/* Loading state khi đang scan mà chưa có QR */}
                {isScanning && !qrImageUrl && (
                  <div
                    className="d-flex align-items-center justify-content-center"
                    style={{
                      width: "300px",
                      height: "300px",
                      backgroundColor: "#f8f9fa",
                      border: "2px solid #198754",
                      borderRadius: "8px",
                      position: "relative",
                    }}
                  >
                    <div className="text-muted">
                      <Icon icon="ph:qr-code" size={64} />
                    </div>

                    {/* HUD elements cho loading state */}
                    <div
                      className="scan-line position-absolute"
                      style={{
                        width: "100%",
                        height: "2px",
                        background:
                          "linear-gradient(90deg, transparent, #198754, transparent)",
                        left: "0",
                        animation: "scanDown 2s ease-in-out infinite",
                        boxShadow: "0 0 10px #198754",
                        zIndex: 10,
                      }}
                    />
                    <div
                      className="position-absolute"
                      style={{
                        top: "10px",
                        left: "10px",
                        width: "20px",
                        height: "20px",
                        borderTop: "3px solid #198754",
                        borderLeft: "3px solid #198754",
                      }}
                    />
                    <div
                      className="position-absolute"
                      style={{
                        top: "10px",
                        right: "10px",
                        width: "20px",
                        height: "20px",
                        borderTop: "3px solid #198754",
                        borderRight: "3px solid #198754",
                      }}
                    />
                    <div
                      className="position-absolute"
                      style={{
                        bottom: "10px",
                        left: "10px",
                        width: "20px",
                        height: "20px",
                        borderBottom: "3px solid #198754",
                        borderLeft: "3px solid #198754",
                      }}
                    />
                    <div
                      className="position-absolute"
                      style={{
                        bottom: "10px",
                        right: "10px",
                        width: "20px",
                        height: "20px",
                        borderBottom: "3px solid #198754",
                        borderRight: "3px solid #198754",
                      }}
                    />

                    <div
                      className="position-absolute w-100 text-center"
                      style={{
                        bottom: "20px",
                        color: "#198754",
                        fontSize: "14px",
                        fontWeight: "bold",
                        textShadow: "0 0 10px #198754",
                        animation:
                          "scanPulse 1s ease-in-out infinite alternate",
                      }}
                    >
                      ĐANG TẠO QR CODE...
                    </div>
                  </div>
                )}
              </div>

              {/* Fallback message nếu tất cả URLs đều fail */}
              {!qrImageUrl && !isScanning && (
                <div className="text-muted p-4 border rounded text-center mt-3">
                  <Icon icon="ph:warning" className="mb-2" size={24} />
                  <div>Không thể tải QR Code</div>
                  <small>
                    Vui lòng sử dụng nút "Mở trong tab mới" bên dưới
                  </small>
                </div>
              )}
            </div>

            {/* CSS Animations */}
            <style jsx>{`
              @keyframes scanDown {
                0% {
                  top: 0;
                }
                50% {
                  top: 50%;
                }
                100% {
                  top: 100%;
                }
              }

              @keyframes scanPulse {
                0% {
                  opacity: 0.7;
                }
                100% {
                  opacity: 1;
                }
              }

              .qr-ready {
                animation: qrAppear 0.5s ease-out forwards;
              }

              @keyframes qrAppear {
                0% {
                  opacity: 0;
                  transform: scale(0.8);
                }
                100% {
                  opacity: 1;
                  transform: scale(1);
                }
              }
            `}</style>
          </div>

          <div className="modal-footer justify-content-center">
            <button
              type="button"
              className="btn btn-secondary me-2"
              onClick={onClose}
            >
              <Icon icon="ph:x" className="me-1" />
              Đóng
            </button>
            <button
              type="button"
              className="btn btn-info me-2"
              onClick={downloadQR}
              disabled={!qrImageUrl}
            >
              <Icon icon="ph:download" className="me-1" />
              Tải QR
            </button>
            <button
              type="button"
              className="btn btn-success"
              onClick={openQRLink}
            >
              <Icon icon="ph:external-link" className="me-1" />
              Mở trong tab mới
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRCodeModal;
