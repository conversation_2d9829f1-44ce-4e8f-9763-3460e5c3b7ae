import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate, useLocation } from "react-router-dom";
import useBankApi from "../callapi/Bank.jsx";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import useBootstrapTooltip from "../hook/useBootstrapTooltip.js";
// === COMPONENT MODAL XÁC NHẬN XÓA (Không thay đổi) ===
const ConfirmDeleteModal = ({
  isOpen,
  onClose,
  onConfirm,
  accountName,
  loading,
}) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: "8px",
          width: "90%",
          maxWidth: "400px",
          boxShadow: "0 5px 15px rgba(0,0,0,.5)",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{ padding: "2rem", textAlign: "center" }}>
          <Icon
            icon="ph:warning-circle-bold"
            className="text-danger mb-3"
            style={{ fontSize: "4rem" }}
          />
          <h5 className="fw-bold">Xác nhận xóa</h5>
          <p className="text-muted">
            Bạn có chắc chắn muốn xóa tài khoản <br />
            <strong className="text-dark">{accountName}</strong> không? Hành
            động này không thể hoàn tác.
          </p>
        </div>
        <div
          style={{
            padding: "1rem",
            borderTop: "1px solid #dee2e6",
            display: "flex",
            justifyContent: "flex-end",
            gap: "0.5rem",
          }}
        >
          <button
            type="button"
            className="btn btn-light border"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </button>
          <button
            type="button"
            className="btn btn-danger"
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? "Đang xóa..." : "Xác nhận xóa"}
          </button>
        </div>
      </div>
    </div>
  );
};

// === COMPONENT MODAL NHẬP OTP XÁC NHẬN XÓA ===
const ConfirmOtpModal = ({ isOpen, onClose, onSubmit, loading, error }) => {
  const [otp, setOtp] = useState("");
  if (!isOpen) return null;
  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0,0,0,0.6)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1050,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: 8,
          width: "90%",
          maxWidth: 400,
          padding: "1rem",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <h5 className="fw-bold">Nhập mã OTP</h5>
        <p className="text-muted">
          Vui lòng nhập mã OTP đã được gửi đến số điện thoại của bạn.
        </p>
        <input
          type="text"
          className="form-control mb-2"
          value={otp}
          onChange={(e) => setOtp(e.target.value)}
          placeholder="OTP code"
        />
        {error && <p className="text-danger small">{error}</p>}
        <div className="mt-3 d-flex justify-content-end gap-2">
          <button
            className="btn btn-secondary"
            onClick={onClose}
            disabled={loading}
          >
            Hủy
          </button>
          <button
            className="btn btn-primary"
            onClick={() => onSubmit(otp)}
            disabled={loading || !otp}
          >
            {loading ? "Đang xác nhận..." : "Xác nhận"}
          </button>
        </div>
      </div>
    </div>
  );
};

// === COMPONENT CHÍNH ===
const AcountBank = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    data: listData,
    loading: listLoading,
    error: listError,
    callApi: getBankAccounts,
  } = useBankApi();
  const {
    // data: actionData, // không còn dùng trực tiếp
    loading: actionLoading,
    error: actionError,
    callApi: performBankAction,
  } = useBankApi();
  const { bankShortName } = useParams();
  const [accounts, setAccounts] = useState([]);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [accountToDelete, setAccountToDelete] = useState(null);
  const [isOtpModalOpen, setIsOtpModalOpen] = useState(false);
  const [otpError, setOtpError] = useState("");

  const fetchAccounts = React.useCallback(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      getBankAccounts({ action: "bank_account", user_id: userId });
    }
  }, [getBankAccounts]);

  // Refetch accounts when page is loaded or when coming back to this page (listen to location.key for navigation)
  useEffect(() => {
    fetchAccounts();
    // eslint-disable-next-line
  }, [fetchAccounts, location.pathname, location.key]);

  // Force refetch when redirected from add-bank page (using location.state)
  useEffect(() => {
    if (location.state && location.state.justAddedBank) {
      fetchAccounts();
      // Clear the state so it doesn't refetch again on next render
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location.state, fetchAccounts, location.pathname]);

  useEffect(() => {
    if (listData && listData.banks && Array.isArray(listData.banks)) {
      // Lọc theo ngân hàng trước
      const bankAccounts = listData.banks.filter(
        (acc) => acc.shortBankName === bankShortName
      );

      // Tạo Map để group các tài khoản theo accountNumber
      const accountMap = new Map();

      bankAccounts.forEach((acc) => {
        const accountNumber = acc.accountNumber;

        if (!accountMap.has(accountNumber)) {
          // Tạo main account entry
          accountMap.set(accountNumber, {
            ...acc,
            vaAccounts: [], // Danh sách VA accounts thuộc tài khoản này
          });
        }

        // Nếu có vaNumber và khác accountNumber, thêm vào danh sách VA
        if (acc.vaNumber && acc.vaNumber !== acc.accountNumber) {
          const mainAccount = accountMap.get(accountNumber);
          mainAccount.vaAccounts.push(acc);

          // Cập nhật tổng giao dịch và số tiền cho main account
          mainAccount.total_amount =
            (mainAccount.total_amount || 0) + (acc.total_amount || 0);
          mainAccount.transaction_count =
            (mainAccount.transaction_count || 0) + (acc.transaction_count || 0);
        }
      });

      // Chuyển Map thành Array và cập nhật trạng thái tổng hợp
      const accountsArray = Array.from(accountMap.values()).map((account) => {
        if (account.vaAccounts && account.vaAccounts.length > 0) {
          // Đếm số VA đang hoạt động
          const activeVACount = account.vaAccounts.filter(
            (va) => va.status === 1
          ).length;

          // Cập nhật trạng thái tổng hợp dựa trên VA
          const isAnyVAActive = activeVACount > 0;

          return {
            ...account,
            activeVACount,
            status: isAnyVAActive ? 1 : 0,
            statusText: isAnyVAActive
              ? `${activeVACount} VA hoạt động`
              : "Không hoạt động",
          };
        }
        return account;
      });

      setAccounts(accountsArray);
    }
  }, [listData, bankShortName]);

  const handleStatusToggle = async (accountToToggle) => {
    // Nếu tài khoản có nhiều VA, cần cập nhật trạng thái cho toàn bộ VA
    if (accountToToggle.vaAccounts && accountToToggle.vaAccounts.length > 0) {
      const newStatus = accountToToggle.activeVACount > 0 ? 0 : 1; // Đảo ngược trạng thái

      try {
        const userId = localStorage.getItem("user_id");

        // Cập nhật trạng thái cho toàn bộ VA
        for (const vaAccount of accountToToggle.vaAccounts) {
          await performBankAction({
            action: "change_status",
            user_id: userId,
            bankId: vaAccount.id,
            status: newStatus,
          });
        }

        // Cập nhật state local
        setAccounts((currentAccounts) =>
          currentAccounts.map((acc) =>
            acc.accountNumber === accountToToggle.accountNumber
              ? {
                  ...acc,
                  vaAccounts: acc.vaAccounts.map((va) => ({
                    ...va,
                    status: newStatus,
                  })),
                  activeVACount: newStatus === 1 ? acc.vaAccounts.length : 0,
                  status: newStatus,
                  statusText:
                    newStatus === 1
                      ? `${acc.vaAccounts.length} VA hoạt động`
                      : "Không hoạt động",
                }
              : acc
          )
        );
      } catch (error) {
        alert("Cập nhật trạng thái VA thất bại!");
      }
    } else {
      // Logic cũ cho tài khoản đơn lẻ
      const accountId = accountToToggle.id;
      const newStatus = accountToToggle.status === 1 ? 0 : 1;

      try {
        const userId = localStorage.getItem("user_id");
        await performBankAction({
          action: "change_status",
          user_id: userId,
          bankId: accountId,
          status: newStatus,
        });

        setAccounts((currentAccounts) =>
          currentAccounts.map((acc) =>
            acc.id === accountId
              ? {
                  ...acc,
                  status: newStatus,
                  statusText:
                    newStatus === 1 ? "Đang hoạt động" : "Không hoạt động",
                }
              : acc
          )
        );
      } catch (error) {
        alert("Cập nhật trạng thái thất bại!");
      }
    }
  };

  const handleDeleteClick = (account) => {
    setAccountToDelete(account);
    setIsConfirmModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!accountToDelete) return;

    const userId = localStorage.getItem("user_id");
    const bankId = accountToDelete.id;

    if (!userId || !bankId) {
      alert(
        "Lỗi: Thiếu User ID hoặc Bank ID. Vui lòng đăng nhập lại và thử lại."
      );
      setIsConfirmModalOpen(false);
      return;
    }

    // Kiểm tra nếu là ngân hàng MBB thì cần OTP, các ngân hàng khác xóa trực tiếp
    if (accountToDelete.shortBankName === "MBB") {
      // Gửi yêu cầu xóa để backend gửi OTP cho MBB
      await performBankAction({
        action: "delete",
        user_id: userId,
        bankId: bankId,
      });

      setIsConfirmModalOpen(false);
      setIsOtpModalOpen(true);
      // Sau khi xác nhận OTP mới fetchAccounts trong handleSubmitOtp
    } else {
      // console.log("delete không otp");
      // Xóa trực tiếp cho các ngân hàng khác
      try {
        await performBankAction({
          action: "delete",
          user_id: userId,
          bankId: bankId,
        });

        setIsConfirmModalOpen(false);
        // Cập nhật UI ngay lập tức bằng cách xóa tài khoản khỏi state
        setAccounts((currentAccounts) =>
          currentAccounts.filter((acc) => acc.id !== bankId)
        );
        setAccountToDelete(null);
        fetchAccounts(); // Vẫn gọi để đảm bảo dữ liệu đồng bộ hoàn toàn với server
      } catch (error) {
        alert("Xóa tài khoản thất bại!");
      }
    }
  };

  const handleSubmitOtp = async (otp) => {
    setOtpError("");
    const userId = localStorage.getItem("user_id");
    const bankId = accountToDelete.id;
    try {
      await performBankAction({
        action: "confirm_otp_delete",
        user_id: userId,
        bankId: bankId,
        otp: otp,
        ip: "",
      });
      setIsOtpModalOpen(false);
      // Cập nhật UI ngay lập tức bằng cách xóa tài khoản khỏi state
      setAccounts((currentAccounts) =>
        currentAccounts.filter((acc) => acc.id !== bankId)
      );
      setAccountToDelete(null);
      fetchAccounts(); // Vẫn gọi để đảm bảo dữ liệu đồng bộ hoàn toàn với server
    } catch (err) {
      setOtpError(err.message || "Xác nhận OTP thất bại");
    }
  };

  const formatCurrency = (amount) =>
    new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount || 0);
  useBootstrapTooltip([listLoading]);
  return (
    <MasterLayout>
      <Breadcrumb
        title={`Danh sách tài khoản ${bankShortName || ""}`}
        items={[
          { label: "Ngân hàng", href: "/client/bank" },
          { label: `Tài khoản ${bankShortName}` },
        ]}
      />
      <div className="container-fluid mt-4">
        {actionError && (
          <div className="alert alert-danger">
            Thao tác thất bại: {actionError}
          </div>
        )}

        <div className="card">
          <div className="card-header d-flex justify-content-between align-items-center bg-light">
            <Link
              to={`/client/add-bank?bank=${bankShortName}`}
              className="btn btn-success d-flex align-items-center"
              style={{ boxShadow: "0 2px 4px rgba(0,0,0,0.1)" }}
            >
              <Icon icon="ph:plus-bold" className="me-2" />
              Thêm tài khoản {bankShortName}
            </Link>
          </div>
          <div className="card-body my-3">
            {listLoading ? (
              <p className="text-center p-5">Đang tải danh sách tài khoản...</p>
            ) : listError ? (
              <p className="text-center text-danger p-5">Lỗi: {listError}</p>
            ) : (
              <div className="table-responsive">
                <table className="table bordered-table mb-0">
                  <thead className="table-light">
                    <tr>
                      <th>ID</th>
                      <th>Tài khoản</th>
                      <th>
                        Tài khoản ảo (VA){" "}
                        <span
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          data-bs-title="Tài khoản ảo (Virtual Account - VA) thuộc tài khoản ngân hàng chính, hữu ích để phân biệt các khoản thu từ các nguồn khác nhau. Với ngân hàng kết nối qua OpenAPI, giao dịch phải qua VA thì Pay2S mới nhận được dữ liệu."
                        >
                          <Icon icon="ph:question" />
                        </span>
                      </th>
                      <th>Tổng tiền GD</th>
                      <th>Tổng số GD</th>
                      <th style={{ width: "150px", minWidth: "150px" }}>
                        Trạng thái
                      </th>
                      <th className="text-center">Hoạt động</th>
                      <th>Quản lý tài khoản</th>
                    </tr>
                  </thead>
                  <tbody>
                    {accounts.map((acc, index) => (
                      <tr
                        key={acc.id}
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          navigate(
                            `/client/account-bank/${bankShortName}/${acc.accountNumber}`
                          )
                        }
                      >
                        <td>{index + 1}</td>
                        <td>
                          <div className="fw-bold">{acc.name}</div>
                          <div className="text-muted">{acc.accountNumber}</div>
                          <div className="badge rounded-pill bg-success">
                            {acc.type}
                          </div>
                        </td>
                        <td>
                          {acc.vaAccounts && acc.vaAccounts.length > 0 ? (
                            <span className="badge bg-success text-white">
                              {acc.vaAccounts.length} VA
                            </span>
                          ) : (
                            <span className="text-muted">-</span>
                          )}
                        </td>
                        <td>{formatCurrency(acc.total_amount)}</td>
                        <td>{acc.transaction_count}</td>
                        <td>
                          <span
                            className={`w-100 badge ${
                              acc.status === 1 ? "bg-success" : "bg-secondary"
                            }`}
                          >
                            {acc.statusText}
                          </span>
                        </td>
                        <td className="text-center">
                          {/* Ẩn switch nếu tài khoản có nhiều VA */}
                          {acc.vaAccounts && acc.vaAccounts.length >= 2 ? (
                            <span className="text-muted">-</span>
                          ) : (
                            <label
                              className="custom-switch"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <input
                                type="checkbox"
                                checked={acc.status === 1}
                                onChange={() => handleStatusToggle(acc)}
                                disabled={actionLoading}
                              />
                              <span className="slider round"></span>
                            </label>
                          )}
                        </td>

                        <td onClick={(e) => e.stopPropagation()}>
                          <button
                            className="btn btn-sm btn-success"
                            onClick={() =>
                              navigate(
                                `/client/account-bank/${bankShortName}/${acc.accountNumber}`
                              )
                            }
                          >
                            <Icon icon="ph:gear" className="text-white me-1" />
                          </button>
                          {/* Ẩn nút xóa nếu tài khoản có VA accounts */}
                          {acc.vaAccounts && acc.vaAccounts.length > 0 ? (
                            <span className="text-muted mx-3"></span>
                          ) : (
                            <button
                              className="btn btn-sm btn-success mx-3"
                              onClick={() => handleDeleteClick(acc)}
                            >
                              <Icon icon="ph:trash" className="text-white" />
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                    {accounts.length === 0 && (
                      <tr>
                        <td colSpan="9" className="text-center p-5">
                          <div className="text-muted">
                            <Icon
                              icon="ph:bank"
                              style={{ fontSize: "3rem" }}
                              className="mb-3 text-secondary"
                            />
                            <h6 className="mb-2">Chưa có tài khoản nào</h6>
                            <p className="mb-3">
                              Bạn chưa thêm tài khoản nào cho ngân hàng{" "}
                              {bankShortName}.
                            </p>
                            <Link
                              to={`/client/add-bank?bank=${bankShortName}`}
                              className="btn btn-success"
                            >
                              <Icon icon="ph:plus" className="me-2" />
                              Thêm tài khoản đầu tiên
                            </Link>
                          </div>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>

      <ConfirmDeleteModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmDelete}
        accountName={accountToDelete?.accountNumber}
        loading={actionLoading}
      />
      <ConfirmOtpModal
        isOpen={isOtpModalOpen}
        onClose={() => setIsOtpModalOpen(false)}
        onSubmit={handleSubmitOtp}
        loading={actionLoading}
        error={otpError}
      />
    </MasterLayout>
  );
};

export default AcountBank;
