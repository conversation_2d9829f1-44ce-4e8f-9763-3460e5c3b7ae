import React, { useState, useEffect } from "react"; // 1. Import useState, useEffect
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { Icon } from "@iconify/react/dist/iconify.js";
// import Affiliate from "../callapi/Affiliate";

import AffiliateDashboard from "../components/AffiliateDashboard";
import useAffiliateApi from "../callapi/AffiliateApi";

const AffiliateUserPage = () => {
  // 3. Hook API và state để quản lý
  const [isRegistered, setIsRegistered] = useState(false);
  const {
    data: affData,
    loading: affLoading,
    error: affError,
    callApi,
  } = useAffiliateApi();

  // Kiểm tra xem user đã đăng ký affiliate chưa khi vào trang
  useEffect(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      callApi({ action: "getAffiliateInfo", user_id: userId });
    }
  }, [callApi]);

  // Khi API trả về, nếu đã đăng ký thì setIsRegistered
  useEffect(() => {
    // Nếu data array có thông tin, xem như đã đăng ký
    if (affData && Array.isArray(affData.data) && affData.data.length > 0) {
      setIsRegistered(true);
    }
  }, [affData]);

  // 4. Xử lý đăng ký
  const handleRegisterClick = async () => {
    console.log("AffiliateRegister: button clicked");
    const userId = localStorage.getItem("user_id");
    console.log("AffiliateRegister: userId =", userId);
    if (!userId) return;
    const result = await callApi({ action: "register", user_id: userId });
    console.log("AffiliateRegister: API result =", result);
    if (result && result.status === true) {
      setIsRegistered(true);
    }
  };

  return (
    <MasterLayout>
      <Breadcrumb title="Tiếp thị liên kết" />

      <div>
        {/* Tab điều hướng - Đã được cập nhật để hiển thị đúng */}
        <div className="d-flex align-items-center">
          <div className="m-3">Tổng quan</div>
          <div>
            <button type="button" className="btn btn-success">
              Rút tiền
            </button>
          </div>
        </div>

        {/* 5. Render có điều kiện dựa trên state 'isRegistered' */}
        {isRegistered ? (
          // Nếu đã đăng ký, hiển thị Dashboard với dữ liệu từ API
          <AffiliateDashboard
            report={affData?.report}
            userList={affData?.data}
          />
        ) : (
          // Nếu chưa, hiển thị giao diện mời đăng ký
          <div className="card mt-3">
            <div className="card-body text-center p-5">
              <p className="mb-3 fs-5">
                Đăng ký tiếp thị liên kết, hoa hồng lên đến 30%
                <span
                  className="ms-1 text-muted"
                  data-bs-toggle="tooltip"
                  title="Nhận hoa hồng khi giới thiệu người dùng mới thành công!"
                >
                  <Icon
                    icon="mdi:information-outline"
                    style={{ cursor: "pointer" }}
                  />
                </span>
              </p>
              {/* Gắn hàm xử lý vào sự kiện onClick của nút */}
              <button
                className="btn btn-success fw-bold"
                onClick={handleRegisterClick}
                disabled={affLoading}
              >
                ĐĂNG KÝ NGAY
              </button>
              {affError && <p className="text-danger mt-2">Lỗi: {affError}</p>}
            </div>
          </div>
        )}
      </div>
    </MasterLayout>
  );
};

export default AffiliateUserPage;
