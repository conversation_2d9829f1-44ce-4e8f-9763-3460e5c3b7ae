import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const VAAccountsTable = ({
  vaAccounts,
  actionLoading,
  onStatusToggle,
  onQRClick,
  onDeleteClick,
  onAddVA,
  onViewHistory,
  formatCurrency,
}) => {
  return (
    <div className="card mt-3">
      <div className="card-body my-3">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <p className="mb-0 fw-bold d-flex align-items-center">
            Tài kho<PERSON>n <PERSON>o (VA)
          </p>
          <button
            className="btn btn-success btn-sm"
            onClick={onAddVA}
            disabled={actionLoading}
          >
            <Icon icon="ph:plus" className="me-1" />
            Thêm VA
          </button>
        </div>
        <div className="card-body p-0">
          <div className="table-responsive">
            <table className="table bordered-table mb-0">
              <thead className="table-light">
                <tr>
                  <th>ID</th>
                  <th>Tài k<PERSON>n ảo</th>
                  <th>Tổng tiền GD</th>
                  <th>Tổng số GD</th>
                  <th style={{ width: "120px", minWidth: "120px" }}>
                    Trạng thái
                  </th>
                  <th className="text-center">Hoạt động</th>
                  <th className="text-center">Lịch sử giao dịch</th>
                  <th>Hành động</th>
                </tr>
              </thead>
              <tbody>
                {vaAccounts && vaAccounts.length > 0 ? (
                  vaAccounts.map((va, index) => (
                    <tr key={va.id || index}>
                      <td>{index + 1}</td>
                      <td>
                        <div className="fw-bold">{va.name || "N/A"}</div>
                        <div className="text-muted">
                          {va.vaNumber || va.accountNumber}
                        </div>
                        <div className="badge rounded-pill bg-success">
                          {va.type || "VA"}
                        </div>
                      </td>
                      <td>{formatCurrency(va.total_amount)}</td>
                      <td>{va.transaction_count || 0}</td>
                      <td>
                        <span
                          className={`w-100 badge ${
                            va.status === 1 ? "bg-success" : "bg-secondary"
                          }`}
                        >
                          {va.status === 1
                            ? "Hoạt động"
                            : va.statusText || "Không hoạt động"}
                        </span>
                      </td>
                      <td className="text-center">
                        <label
                          className="custom-switch"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <input
                            type="checkbox"
                            checked={va.status === 1}
                            onChange={() => onStatusToggle(va)}
                            disabled={actionLoading}
                          />
                          <span className="slider round"></span>
                        </label>
                      </td>
                      <td className="text-center">
                        <button
                          className="btn btn-primary btn-sm"
                          onClick={() => onViewHistory(va)}
                          disabled={actionLoading}
                          title="Xem lịch sử giao dịch"
                        >
                          <Icon
                            icon="ph:clock-counter-clockwise"
                            className="me-1"
                          />
                          Lịch sử
                        </button>
                      </td>
                      <td>
                        <button
                          className="btn btn-success btn-sm me-1"
                          onClick={() => onQRClick(va)}
                          disabled={actionLoading}
                          title="Tạo QR Code"
                        >
                          <Icon icon="ph:qr-code" className="me-1" />
                          QR
                        </button>
                        <button
                          className="btn btn-success btn-sm"
                          onClick={() => onDeleteClick(va)}
                          disabled={actionLoading}
                          title="Xóa tài khoản ảo"
                        >
                          <Icon icon="ph:trash" className="me-1" />
                          Xóa
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="8" className="text-center text-muted p-4">
                      Chưa có tài khoản ảo nào
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          <div className="d-flex justify-content-between align-items-center p-3">
            <span className="text-muted">
              Đang xem 1 đến {vaAccounts?.length || 0} trong tổng số{" "}
              {vaAccounts?.length || 0} mục
            </span>
            <nav>
              <ul className="pagination pagination-sm mb-0">
                <li className="page-item disabled">
                  <span className="page-link">Trước</span>
                </li>
                <li className="page-item active">
                  <span className="page-link bg-success border-success">1</span>
                </li>
                <li className="page-item disabled">
                  <span className="page-link">Tiếp</span>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VAAccountsTable;
