import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import Notifications from "./Notifications";
import UserMenu from "./UserMenu"; // resolve without extension

const Navbar = ({
  sidebarActive,
  sidebarToggle,
  mobileMenuToggle,
  displayName,
  displayPlan,
}) => (
  <div className="navbar-header">
    <div className="row align-items-center justify-content-between">
      <div className="col-auto">
        <div className="d-flex align-items-center gap-4">
          <button
            className="sidebar-toggle"
            type="button"
            onClick={sidebarToggle}
          >
            {sidebarActive ? (
              <Icon
                icon="iconoir:arrow-right"
                className="icon text-2xl non-active"
              />
            ) : (
              <Icon
                icon="heroicons:bars-3-solid"
                className="icon text-2xl non-active"
              />
            )}
          </button>
          <button
            className="sidebar-mobile-toggle"
            type="button"
            onClick={mobileMenuToggle}
          >
            <Icon icon="heroicons:bars-3-solid" className="icon" />
          </button>
        </div>
      </div>
      <div className="col-auto">
        <div className="d-flex align-items-center gap-3">
          <div className="dropdown notification-dropdown">
            <Notifications />
          </div>
          <UserMenu displayName={displayName} displayPlan={displayPlan} />
        </div>
      </div>
    </div>
  </div>
);

export default Navbar;
