// src/client/addbankform/Pay2sBusinessForm.jsx

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useBankApi from "../../callapi/Bank.jsx";

const Pay2sBusinessForm = ({ bankName }) => {
  // === STATE ===
  // State chung cho tất cả các form
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [agree, setAgree] = useState(false);

  // State chỉ dành cho MBB
  const [corpId, setCorpId] = useState("");

  // State cho quy trình OTP
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otp, setOtp] = useState("");

  // State cho thông báo
  const [notification, setNotification] = useState({
    error: null,
    success: null,
  });

  // === HOOKS ===
  const {
    data: apiResponse,
    loading: isLoading,
    error: apiError,
    callApi,
  } = useBankApi();
  const navigate = useNavigate();

  // === HANDLERS ===
  const handleSubmit = async (event) => {
    event.preventDefault();
    setNotification({ error: null, success: null });
    const userId = localStorage.getItem("user_id");

    if (!userId) {
      setNotification({
        error: "Lỗi xác thực: Không tìm thấy User ID. Vui lòng đăng nhập lại.",
        success: null,
      });
      return;
    }

    let apiBody;
    if (showOtpInput) {
      // Giai đoạn 2: Gửi OTP
      apiBody = {
        username,
        password,
        accountNumber,
        shortName: bankName,
        otp,
        action: "confirm_otp",
        user_id: userId,
      };
    } else {
      // Giai đoạn 1: Gửi thông tin ban đầu
      if (!agree) {
        setNotification({
          error: "Bạn phải đồng ý với điều khoản và điều kiện.",
          success: null,
        });
        return;
      }

      // Tạo apiBody cơ bản
      apiBody = {
        username,
        password,
        accountNumber,
        shortName: bankName,
        type: "business",
        action: "add",
        user_id: userId,
      };

      // Nếu là ngân hàng MBB, thêm corp_id vào body
      if (bankName === "MBB") {
        apiBody.corp_id = corpId;
      }
    }

    await callApi(apiBody);
  };

  // === EFFECTS ===
  useEffect(() => {
    if (apiError) {
      setNotification({ error: apiError, success: null });
    }

    if (apiResponse && apiResponse.status === true) {
      if (apiResponse.OTP === 1 && !showOtpInput) {
        setShowOtpInput(true);
        setNotification({
          success: "Gửi OTP Thành công. Vui lòng nhập mã OTP để hoàn tất.",
          error: null,
        });
      } else {
        const finalMessage = apiResponse.message || "Thao tác thành công!";
        setNotification({
          success: `${finalMessage} Đang chuyển hướng...`,
          error: null,
        });

        const timer = setTimeout(() => {
          navigate(`/client/account-bank/${bankName}`, {
            state: { justAddedBank: true },
          });
        }, 2000);

        return () => clearTimeout(timer);
      }
    }
  }, [apiResponse, apiError, navigate, bankName]);

  // === RENDER ===
  return (
    <form onSubmit={handleSubmit}>
      {/* Khu vực hiển thị thông báo */}
      {notification.error && (
        <div className="alert alert-danger">{notification.error}</div>
      )}
      {notification.success && (
        <div className="alert alert-success">{notification.success}</div>
      )}

      {/* Các trường nhập liệu chung */}
      <div className="mt-20">
        <label className="form-label">
          Tên đăng nhập tài khoản {bankName} *
        </label>
        <input
          className="form-control"
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
          disabled={showOtpInput || isLoading}
        />
      </div>
      <div className="mt-20">
        <label className="form-label">Mật khẩu tài khoản {bankName} *</label>
        <input
          className="form-control"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          disabled={showOtpInput || isLoading}
        />
      </div>
      <div className="mt-20">
        <label className="form-label">Số tài khoản {bankName} *</label>
        <input
          className="form-control"
          type="text"
          value={accountNumber}
          onChange={(e) => setAccountNumber(e.target.value)}
          required
          disabled={showOtpInput || isLoading}
        />
      </div>

      {/* Trường nhập liệu có điều kiện cho MBB */}
      {bankName === "MBB" && (
        <div className="mt-20">
          <label className="form-label">Mã doanh nghiệp *</label>
          <input
            className="form-control"
            type="text"
            value={corpId}
            onChange={(e) => setCorpId(e.target.value)}
            required
            disabled={showOtpInput || isLoading}
            placeholder="Nhập mã doanh nghiệp của bạn"
          />
        </div>
      )}

      {/* Trường nhập OTP */}
      {showOtpInput && (
        <div className="mt-20">
          <label className="form-label fw-bold text-danger">Mã OTP *</label>
          <input
            className="form-control"
            type="text"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            required
            placeholder="Nhập mã OTP đã được gửi đến bạn"
            autoFocus
          />
        </div>
      )}

      {/* Checkbox điều khoản */}
      {!showOtpInput && (
        <div className="form-check mt-20">
          <input
            className="form-check-input"
            type="checkbox"
            id="agreePay2s"
            checked={agree}
            onChange={(e) => setAgree(e.target.checked)}
            required
          />
          <label className="form-check-label small" htmlFor="agreePay2s">
            Bằng cách cung cấp thông tin cho Pay2S. Bạn đã đồng ý với{" "}
            <a
              href="https://pay2s.vn/chinh-sach-bao-mat"
              className="text-primary-600 fw-semibold"
              target="_blank"
              rel="noopener noreferrer"
            >
              Chính sách bảo mật *
            </a>{" "}
            của Pay2S và cho phép Pay2S truy xuất thông tin tài chính từ ngân
            hàng của bạn và Đồng ý nhận thông báo tiền về từ ngân hàng đến hệ
            thống Pay2S.
          </label>
        </div>
      )}

      {/* Nút Submit chính của form */}
      <div className="mt-20">
        <button type="submit" className="btn btn-success" disabled={isLoading}>
          {isLoading
            ? "ĐANG XỬ LÝ..."
            : showOtpInput
            ? "XÁC NHẬN OTP"
            : "THÊM TÀI KHOẢN"}
        </button>
      </div>
    </form>
  );
};

export default Pay2sBusinessForm;
